import SwiftUI

struct QuickSearchView: View {
    @EnvironmentObject var appState: AppState
    @State private var searchText = ""
    @FocusState private var isSearchFocused: Bool
    
    var body: some View {
        VStack(spacing: 0) {
            // Search Field
            HStack {
                Image(systemName: "magnifyingglass")
                    .foregroundColor(.secondary)
                
                TextField("Search services, containers, logs, settings...", text: $searchText)
                    .textFieldStyle(.plain)
                    .focused($isSearchFocused)
                    .onSubmit {
                        if let firstResult = appState.searchResults.first {
                            firstResult.action()
                            closeSearch()
                        }
                    }
                
                if !searchText.isEmpty {
                    Button(action: {
                        searchText = ""
                    }) {
                        Image(systemName: "xmark.circle.fill")
                            .foregroundColor(.secondary)
                    }
                    .buttonStyle(.plain)
                }
            }
            .padding()
            .background(Color(NSColor.controlBackgroundColor))
            
            Divider()
            
            // Results
            if !searchText.isEmpty {
                ScrollView {
                    LazyVStack(spacing: 0) {
                        ForEach(filteredResults) { result in
                            SearchResultRow(result: result) {
                                result.action()
                                closeSearch()
                            }
                        }
                    }
                }
                .frame(maxHeight: 400)
            } else {
                // Quick Actions when no search
                VStack(spacing: 12) {
                    Text("Quick Actions")
                        .font(.headline)
                        .foregroundColor(.secondary)
                        .padding(.top)
                    
                    LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 2), spacing: 12) {
                        QuickActionButton(
                            title: "Refresh All",
                            icon: "arrow.clockwise",
                            action: {
                                Task {
                                    await appState.refreshAllData()
                                }
                                closeSearch()
                            }
                        )
                        
                        QuickActionButton(
                            title: "Services",
                            icon: "gear.circle",
                            action: {
                                appState.currentView = .services
                                closeSearch()
                            }
                        )
                        
                        QuickActionButton(
                            title: "Docker",
                            icon: "shippingbox",
                            action: {
                                appState.currentView = .docker
                                closeSearch()
                            }
                        )
                        
                        QuickActionButton(
                            title: "System Metrics",
                            icon: "chart.line.uptrend.xyaxis",
                            action: {
                                appState.currentView = .systemMetrics
                                closeSearch()
                            }
                        )
                        
                        QuickActionButton(
                            title: "Logs",
                            icon: "doc.text",
                            action: {
                                appState.currentView = .logs
                                closeSearch()
                            }
                        )
                        
                        QuickActionButton(
                            title: "Settings",
                            icon: "slider.horizontal.3",
                            action: {
                                appState.showingSettings = true
                                closeSearch()
                            }
                        )
                    }
                    .padding(.horizontal)
                    .padding(.bottom)
                }
            }
        }
        .frame(width: 600, height: searchText.isEmpty ? 250 : min(450, CGFloat(filteredResults.count * 44 + 100)))
        .background(Color(NSColor.windowBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: .black.opacity(0.3), radius: 20, x: 0, y: 10)
        .onAppear {
            isSearchFocused = true
            appState.searchQuery = searchText
        }
        .onChange(of: searchText) { newValue in
            appState.searchQuery = newValue
        }
        .onKeyPress(.escape) {
            closeSearch()
            return .handled
        }
    }
    
    private var filteredResults: [SearchResult] {
        if searchText.isEmpty {
            return []
        }
        return appState.searchResults
    }
    
    private func closeSearch() {
        appState.showingQuickSearch = false
        searchText = ""
        appState.searchQuery = ""
    }
}

struct SearchResultRow: View {
    let result: SearchResult
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: result.category.systemImage)
                    .foregroundColor(.accentColor)
                    .frame(width: 20)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text(result.title)
                        .font(.system(size: 14, weight: .medium))
                        .foregroundColor(.primary)
                    
                    if let subtitle = result.subtitle {
                        Text(subtitle)
                            .font(.system(size: 12))
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                Text(result.category.rawValue)
                    .font(.system(size: 11))
                    .foregroundColor(.secondary)
                    .padding(.horizontal, 6)
                    .padding(.vertical, 2)
                    .background(Color.secondary.opacity(0.1))
                    .clipShape(Capsule())
            }
            .padding(.horizontal, 16)
            .padding(.vertical, 8)
            .contentShape(Rectangle())
        }
        .buttonStyle(.plain)
        .onHover { hovering in
            if hovering {
                NSCursor.pointingHand.push()
            } else {
                NSCursor.pop()
            }
        }
    }
}

struct QuickActionButton: View {
    let title: String
    let icon: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.system(size: 24))
                    .foregroundColor(.accentColor)
                
                Text(title)
                    .font(.system(size: 12, weight: .medium))
                    .foregroundColor(.primary)
            }
            .frame(height: 60)
            .frame(maxWidth: .infinity)
            .background(Color(NSColor.controlBackgroundColor))
            .clipShape(RoundedRectangle(cornerRadius: 8))
        }
        .buttonStyle(.plain)
        .onHover { hovering in
            if hovering {
                NSCursor.pointingHand.push()
            } else {
                NSCursor.pop()
            }
        }
    }
}

#Preview {
    QuickSearchView()
        .environmentObject(AppState())
}