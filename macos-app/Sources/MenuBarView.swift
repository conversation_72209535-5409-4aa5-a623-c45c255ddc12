import SwiftUI

struct MenuBarView: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        VStack(spacing: 0) {
            // Header
            HStack {
                Image(systemName: "server.rack")
                    .foregroundColor(.accentColor)
                
                VStack(alignment: .leading, spacing: 2) {
                    Text("WOW Monitor")
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    HStack(spacing: 4) {
                        Circle()
                            .fill(connectionStatusColor)
                            .frame(width: 6, height: 6)
                        
                        Text(connectionStatusText)
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
                
                Spacer()
                
                Button(action: {
                    NSApp.activate(ignoringOtherApps: true)
                }) {
                    Image(systemName: "arrow.up.right.square")
                        .foregroundColor(.secondary)
                }
                .buttonStyle(.plain)
                .help("Open Main Window")
            }
            .padding()
            
            Divider()
            
            // Quick Stats
            if appState.isConnected {
                VStack(spacing: 8) {
                    // System Overview
                    if let metrics = appState.systemMetrics {
                        QuickStatsRow(
                            icon: "cpu",
                            label: "CPU",
                            value: "\(String(format: "%.1f", metrics.cpu.usage))%",
                            color: cpuColor(usage: metrics.cpu.usage)
                        )
                        
                        QuickStatsRow(
                            icon: "memorychip",
                            label: "Memory",
                            value: "\(String(format: "%.1f", metrics.memory.usagePercentage))%",
                            color: memoryColor(usage: metrics.memory.usagePercentage)
                        )
                        
                        QuickStatsRow(
                            icon: "internaldrive",
                            label: "Disk",
                            value: "\(String(format: "%.1f", metrics.disk.usagePercentage))%",
                            color: diskColor(usage: metrics.disk.usagePercentage)
                        )
                    }
                    
                    Divider()
                    
                    // Services & Containers
                    HStack {
                        VStack(alignment: .leading, spacing: 4) {
                            Text("Services")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            HStack(spacing: 8) {
                                StatusDot(
                                    count: appState.services.filter { $0.status == .running }.count,
                                    color: .green,
                                    label: "Running"
                                )
                                
                                StatusDot(
                                    count: appState.services.filter { $0.status == .stopped }.count,
                                    color: .gray,
                                    label: "Stopped"
                                )
                                
                                StatusDot(
                                    count: appState.services.filter { $0.status == .error }.count,
                                    color: .red,
                                    label: "Error"
                                )
                            }
                        }
                        
                        Spacer()
                        
                        VStack(alignment: .trailing, spacing: 4) {
                            Text("Containers")
                                .font(.caption)
                                .foregroundColor(.secondary)
                            
                            HStack(spacing: 8) {
                                StatusDot(
                                    count: appState.dockerContainers.filter { $0.status == .running }.count,
                                    color: .green,
                                    label: "Running"
                                )
                                
                                StatusDot(
                                    count: appState.dockerContainers.filter { $0.status == .exited }.count,
                                    color: .red,
                                    label: "Stopped"
                                )
                            }
                        }
                    }
                }
                .padding()
                
                Divider()
            }
            
            // Quick Actions
            VStack(spacing: 4) {
                MenuBarButton(
                    title: "Dashboard",
                    icon: "rectangle.3.group",
                    action: {
                        appState.currentView = .dashboard
                        NSApp.activate(ignoringOtherApps: true)
                    }
                )
                
                MenuBarButton(
                    title: "Services",
                    icon: "gear.circle",
                    action: {
                        appState.currentView = .services
                        NSApp.activate(ignoringOtherApps: true)
                    }
                )
                
                MenuBarButton(
                    title: "Docker",
                    icon: "shippingbox",
                    action: {
                        appState.currentView = .docker
                        NSApp.activate(ignoringOtherApps: true)
                    }
                )
                
                Divider()
                
                MenuBarButton(
                    title: appState.isConnected ? "Disconnect" : "Connect",
                    icon: appState.isConnected ? "wifi.slash" : "wifi",
                    action: {
                        if appState.isConnected {
                            appState.disconnectFromServer()
                        } else {
                            Task {
                                await appState.connectToServer()
                            }
                        }
                    }
                )
                
                MenuBarButton(
                    title: "Refresh",
                    icon: "arrow.clockwise",
                    action: {
                        Task {
                            await appState.refreshAllData()
                        }
                    }
                )
                
                Divider()
                
                MenuBarButton(
                    title: "Quit",
                    icon: "power",
                    action: {
                        NSApp.terminate(nil)
                    }
                )
            }
            .padding(.vertical, 8)
        }
        .frame(width: 280)
    }
    
    // MARK: - Helper Properties
    
    private var connectionStatusColor: Color {
        switch appState.connectionStatus {
        case .connected:
            return .green
        case .connecting:
            return .orange
        case .error:
            return .red
        case .disconnected:
            return .gray
        }
    }
    
    private var connectionStatusText: String {
        switch appState.connectionStatus {
        case .connected:
            return "Connected"
        case .connecting:
            return "Connecting..."
        case .error:
            return "Connection Error"
        case .disconnected:
            return "Disconnected"
        }
    }
    
    private func cpuColor(usage: Double) -> Color {
        if usage > 90 { return .red }
        if usage > 70 { return .orange }
        return .green
    }
    
    private func memoryColor(usage: Double) -> Color {
        if usage > 95 { return .red }
        if usage > 80 { return .orange }
        return .green
    }
    
    private func diskColor(usage: Double) -> Color {
        if usage > 95 { return .red }
        if usage > 85 { return .orange }
        return .green
    }
}

struct QuickStatsRow: View {
    let icon: String
    let label: String
    let value: String
    let color: Color
    
    var body: some View {
        HStack {
            Image(systemName: icon)
                .foregroundColor(.secondary)
                .frame(width: 16)
            
            Text(label)
                .font(.caption)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .font(.caption)
                .fontWeight(.medium)
                .foregroundColor(color)
        }
    }
}

struct StatusDot: View {
    let count: Int
    let color: Color
    let label: String
    
    var body: some View {
        HStack(spacing: 2) {
            Circle()
                .fill(color)
                .frame(width: 6, height: 6)
            
            Text("\(count)")
                .font(.caption2)
                .fontWeight(.medium)
                .foregroundColor(.primary)
        }
        .help("\(count) \(label)")
    }
}

struct MenuBarButton: View {
    let title: String
    let icon: String
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(.secondary)
                    .frame(width: 16)
                
                Text(title)
                    .font(.system(size: 13))
                    .foregroundColor(.primary)
                
                Spacer()
            }
            .padding(.horizontal, 12)
            .padding(.vertical, 6)
            .contentShape(Rectangle())
        }
        .buttonStyle(.plain)
        .onHover { hovering in
            if hovering {
                NSCursor.pointingHand.push()
            } else {
                NSCursor.pop()
            }
        }
    }
}

#Preview {
    MenuBarView()
        .environmentObject(AppState())
}