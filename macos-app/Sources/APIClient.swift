import Foundation
import Alamofire

class APIClient {
    private var baseURL: String
    private let session: Session
    
    init(baseURL: String) {
        self.baseURL = baseURL
        
        let configuration = URLSessionConfiguration.default
        configuration.timeoutIntervalForRequest = 30
        configuration.timeoutIntervalForResource = 60
        
        self.session = Session(configuration: configuration)
    }
    
    func updateBaseURL(_ newBaseURL: String) {
        self.baseURL = newBaseURL
    }
    
    // MARK: - Connection Testing
    
    func testConnection() async -> APIResponse<String> {
        return await performRequest(
            endpoint: "/health",
            method: .get,
            responseType: String.self
        )
    }
    
    // MARK: - System Metrics
    
    func getSystemMetrics() async -> APIResponse<SystemMetrics> {
        return await performRequest(
            endpoint: "/api/system/metrics",
            method: .get,
            responseType: SystemMetrics.self
        )
    }
    
    // MARK: - Services
    
    func getServices() async -> APIResponse<[ServiceInfo]> {
        return await performRequest(
            endpoint: "/api/services",
            method: .get,
            responseType: [ServiceInfo].self
        )
    }
    
    func getService(_ serviceId: String) async -> APIResponse<ServiceInfo> {
        return await performRequest(
            endpoint: "/api/services/\(serviceId)",
            method: .get,
            responseType: ServiceInfo.self
        )
    }
    
    func startService(_ serviceId: String) async -> APIResponse<String> {
        return await performRequest(
            endpoint: "/api/services/\(serviceId)/start",
            method: .post,
            responseType: String.self
        )
    }
    
    func stopService(_ serviceId: String) async -> APIResponse<String> {
        return await performRequest(
            endpoint: "/api/services/\(serviceId)/stop",
            method: .post,
            responseType: String.self
        )
    }
    
    func restartService(_ serviceId: String) async -> APIResponse<String> {
        return await performRequest(
            endpoint: "/api/services/\(serviceId)/restart",
            method: .post,
            responseType: String.self
        )
    }
    
    // MARK: - Docker
    
    func getDockerContainers() async -> APIResponse<[DockerContainer]> {
        return await performRequest(
            endpoint: "/api/docker/containers",
            method: .get,
            responseType: [DockerContainer].self
        )
    }
    
    func getDockerImages() async -> APIResponse<[DockerImage]> {
        return await performRequest(
            endpoint: "/api/docker/images",
            method: .get,
            responseType: [DockerImage].self
        )
    }
    
    func getContainer(_ containerId: String) async -> APIResponse<DockerContainer> {
        return await performRequest(
            endpoint: "/api/docker/containers/\(containerId)",
            method: .get,
            responseType: DockerContainer.self
        )
    }
    
    func startContainer(_ containerId: String) async -> APIResponse<String> {
        return await performRequest(
            endpoint: "/api/docker/containers/\(containerId)/start",
            method: .post,
            responseType: String.self
        )
    }
    
    func stopContainer(_ containerId: String) async -> APIResponse<String> {
        return await performRequest(
            endpoint: "/api/docker/containers/\(containerId)/stop",
            method: .post,
            responseType: String.self
        )
    }
    
    func restartContainer(_ containerId: String) async -> APIResponse<String> {
        return await performRequest(
            endpoint: "/api/docker/containers/\(containerId)/restart",
            method: .post,
            responseType: String.self
        )
    }
    
    func removeContainer(_ containerId: String) async -> APIResponse<String> {
        return await performRequest(
            endpoint: "/api/docker/containers/\(containerId)",
            method: .delete,
            responseType: String.self
        )
    }
    
    // MARK: - Logs
    
    func getRecentLogs(limit: Int = 100, level: LogLevel? = nil) async -> APIResponse<[LogEntry]> {
        var parameters: [String: Any] = ["limit": limit]
        if let level = level {
            parameters["level"] = level.rawValue
        }
        
        return await performRequest(
            endpoint: "/api/logs",
            method: .get,
            parameters: parameters,
            responseType: [LogEntry].self
        )
    }
    
    func getServiceLogs(_ serviceId: String, limit: Int = 100) async -> APIResponse<[LogEntry]> {
        return await performRequest(
            endpoint: "/api/services/\(serviceId)/logs",
            method: .get,
            parameters: ["limit": limit],
            responseType: [LogEntry].self
        )
    }
    
    func getContainerLogs(_ containerId: String, limit: Int = 100) async -> APIResponse<[LogEntry]> {
        return await performRequest(
            endpoint: "/api/docker/containers/\(containerId)/logs",
            method: .get,
            parameters: ["limit": limit],
            responseType: [LogEntry].self
        )
    }
    
    // MARK: - Private Methods
    
    private func performRequest<T: Codable>(
        endpoint: String,
        method: HTTPMethod,
        parameters: [String: Any]? = nil,
        headers: HTTPHeaders? = nil,
        responseType: T.Type
    ) async -> APIResponse<T> {
        let url = baseURL + endpoint
        
        return await withCheckedContinuation { continuation in
            var request = session.request(
                url,
                method: method,
                parameters: parameters,
                encoding: method == .get ? URLEncoding.default : JSONEncoding.default,
                headers: headers
            )
            
            request.responseData { response in
                let apiResponse = self.handleResponse(response: response, responseType: responseType)
                continuation.resume(returning: apiResponse)
            }
        }
    }
    
    private func handleResponse<T: Codable>(
        response: AFDataResponse<Data>,
        responseType: T.Type
    ) -> APIResponse<T> {
        let timestamp = Date()
        
        switch response.result {
        case .success(let data):
            // Try to decode as APIResponse<T> first
            if let apiResponse = try? JSONDecoder().decode(APIResponse<T>.self, from: data) {
                return apiResponse
            }
            
            // If that fails, try to decode the data directly as T
            if let decodedData = try? JSONDecoder().decode(T.self, from: data) {
                return APIResponse(
                    success: true,
                    data: decodedData,
                    error: nil,
                    timestamp: timestamp
                )
            }
            
            // If it's a simple success response (like for actions)
            if T.self == String.self, response.response?.statusCode == 200 {
                return APIResponse(
                    success: true,
                    data: "Success" as? T,
                    error: nil,
                    timestamp: timestamp
                )
            }
            
            // Handle error responses
            if let errorResponse = try? JSONDecoder().decode(ErrorResponse.self, from: data) {
                return APIResponse(
                    success: false,
                    data: nil,
                    error: errorResponse.message,
                    timestamp: timestamp
                )
            }
            
            return APIResponse(
                success: false,
                data: nil,
                error: "Invalid response format",
                timestamp: timestamp
            )
            
        case .failure(let error):
            let errorMessage: String
            
            if let afError = error.asAFError {
                switch afError {
                case .sessionTaskFailed(let sessionError):
                    if let urlError = sessionError as? URLError {
                        switch urlError.code {
                        case .notConnectedToInternet:
                            errorMessage = "No internet connection"
                        case .timedOut:
                            errorMessage = "Request timed out"
                        case .cannotConnectToHost:
                            errorMessage = "Cannot connect to server"
                        default:
                            errorMessage = urlError.localizedDescription
                        }
                    } else {
                        errorMessage = sessionError.localizedDescription
                    }
                case .responseValidationFailed(let reason):
                    errorMessage = "Response validation failed: \(reason)"
                default:
                    errorMessage = afError.localizedDescription
                }
            } else {
                errorMessage = error.localizedDescription
            }
            
            return APIResponse(
                success: false,
                data: nil,
                error: errorMessage,
                timestamp: timestamp
            )
        }
    }
}

// MARK: - Supporting Types

private struct ErrorResponse: Codable {
    let message: String
    let code: String?
}