import Foundation
import Starscream
import Combine

class SocketManager: ObservableObject, WebSocketDelegate {
    @Published var isConnected = false
    @Published var connectionError: String?
    
    private var socket: WebSocket?
    private var url: String
    private let messageSubject = PassthroughSubject<WebSocketMessage, Never>()
    
    var messagePublisher: AnyPublisher<WebSocketMessage, Never> {
        messageSubject.eraseToAnyPublisher()
    }
    
    init(url: String) {
        self.url = url
    }
    
    func updateURL(_ newURL: String) {
        let wasConnected = isConnected
        disconnect()
        self.url = newURL
        
        if wasConnected {
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                Task {
                    await self.connect()
                }
            }
        }
    }
    
    @MainActor
    func connect() async {
        guard !url.isEmpty else {
            connectionError = "WebSocket URL is not configured"
            return
        }
        
        guard let socketURL = URL(string: url) else {
            connectionError = "Invalid WebSocket URL"
            return
        }
        
        var request = URLRequest(url: socketURL)
        request.timeoutInterval = 30
        
        socket = WebSocket(request: request)
        socket?.delegate = self
        socket?.connect()
    }
    
    func disconnect() {
        socket?.disconnect()
        socket = nil
        isConnected = false
    }
    
    func send(message: WebSocketMessage) {
        guard isConnected else { return }
        
        do {
            let data = try JSONEncoder().encode(message)
            socket?.write(data: data)
        } catch {
            print("Failed to encode WebSocket message: \(error)")
        }
    }
    
    // MARK: - WebSocketDelegate
    
    func didReceive(event: Starscream.WebSocketEvent, client: Starscream.WebSocketClient) {
        DispatchQueue.main.async {
            switch event {
            case .connected(let headers):
                print("WebSocket connected: \(headers)")
                self.isConnected = true
                self.connectionError = nil
                
            case .disconnected(let reason, let code):
                print("WebSocket disconnected: \(reason) with code: \(code)")
                self.isConnected = false
                
                // Auto-reconnect after a delay if not a clean disconnect
                if code != 1000 {
                    DispatchQueue.main.asyncAfter(deadline: .now() + 5.0) {
                        Task {
                            await self.connect()
                        }
                    }
                }
                
            case .text(let string):
                self.handleTextMessage(string)
                
            case .binary(let data):
                self.handleBinaryMessage(data)
                
            case .ping:
                break
                
            case .pong:
                break
                
            case .viabilityChanged(let viable):
                print("WebSocket viability changed: \(viable)")
                
            case .reconnectSuggested(let suggested):
                print("WebSocket reconnect suggested: \(suggested)")
                if suggested {
                    Task {
                        await self.connect()
                    }
                }
                
            case .cancelled:
                print("WebSocket cancelled")
                self.isConnected = false
                
            case .error(let error):
                print("WebSocket error: \(error?.localizedDescription ?? "Unknown error")")
                self.connectionError = error?.localizedDescription ?? "Unknown WebSocket error"
                self.isConnected = false
                
                // Auto-reconnect after a delay
                DispatchQueue.main.asyncAfter(deadline: .now() + 10.0) {
                    Task {
                        await self.connect()
                    }
                }
            }
        }
    }
    
    // MARK: - Message Handling
    
    private func handleTextMessage(_ text: String) {
        guard let data = text.data(using: .utf8) else {
            print("Failed to convert text message to data")
            return
        }
        
        handleBinaryMessage(data)
    }
    
    private func handleBinaryMessage(_ data: Data) {
        do {
            let message = try JSONDecoder().decode(WebSocketMessage.self, from: data)
            messageSubject.send(message)
        } catch {
            print("Failed to decode WebSocket message: \(error)")
            
            // Try to decode as a simple message format
            if let simpleMessage = try? JSONSerialization.jsonObject(with: data) as? [String: Any],
               let type = simpleMessage["type"] as? String,
               let messageType = MessageType(rawValue: type) {
                
                let webSocketMessage = WebSocketMessage(
                    type: messageType,
                    payload: data,
                    timestamp: Date()
                )
                
                messageSubject.send(webSocketMessage)
            }
        }
    }
    
    // MARK: - Convenience Methods
    
    func subscribeToMetrics() {
        let message = WebSocketMessage(
            type: .systemMetrics,
            payload: Data(),
            timestamp: Date()
        )
        send(message: message)
    }
    
    func subscribeToServices() {
        let message = WebSocketMessage(
            type: .serviceUpdate,
            payload: Data(),
            timestamp: Date()
        )
        send(message: message)
    }
    
    func subscribeToDocker() {
        let message = WebSocketMessage(
            type: .dockerUpdate,
            payload: Data(),
            timestamp: Date()
        )
        send(message: message)
    }
    
    func subscribeToLogs() {
        let message = WebSocketMessage(
            type: .logEntry,
            payload: Data(),
            timestamp: Date()
        )
        send(message: message)
    }
}