import Foundation
import UserNotifications
import AppKit

@MainActor
class NotificationManager: NSObject, ObservableObject, UNUserNotificationCenterDelegate {
    
    override init() {
        super.init()
        UNUserNotificationCenter.current().delegate = self
        requestNotificationPermissions()
    }
    
    private func requestNotificationPermissions() {
        UNUserNotificationCenter.current().requestAuthorization(options: [.alert, .sound, .badge]) { granted, error in
            DispatchQueue.main.async {
                if granted {
                    print("Notification permissions granted")
                } else {
                    print("Notification permissions denied: \(error?.localizedDescription ?? "Unknown error")")
                }
            }
        }
    }
    
    func showNotification(title: String, message: String, type: NotificationType) async {
        let content = UNMutableNotificationContent()
        content.title = title
        content.body = message
        content.sound = .default
        
        // Add type-specific customizations
        switch type {
        case .error, .warning:
            content.sound = UNNotificationSound(named: UNNotificationSoundName("alert.aiff"))
        case .success:
            content.sound = UNNotificationSound(named: UNNotificationSoundName("success.aiff"))
        case .info:
            content.sound = .default
        }
        
        // Create identifier
        let identifier = UUID().uuidString
        
        // Create request
        let request = UNNotificationRequest(
            identifier: identifier,
            content: content,
            trigger: nil // Immediate notification
        )
        
        do {
            try await UNUserNotificationCenter.current().add(request)
        } catch {
            print("Failed to show notification: \(error)")
        }
    }
    
    func showSystemAlert(title: String, message: String, type: NotificationType, actions: [String] = []) async -> String? {
        return await withCheckedContinuation { continuation in
            let alert = NSAlert()
            alert.messageText = title
            alert.informativeText = message
            
            // Set alert style based on type
            switch type {
            case .error:
                alert.alertStyle = .critical
            case .warning:
                alert.alertStyle = .warning
            case .info, .success:
                alert.alertStyle = .informational
            }
            
            // Add action buttons
            if actions.isEmpty {
                alert.addButton(withTitle: "OK")
            } else {
                for action in actions {
                    alert.addButton(withTitle: action)
                }
            }
            
            // Show alert
            let response = alert.runModal()
            let buttonIndex = response.rawValue - NSApplication.ModalResponse.alertFirstButtonReturn.rawValue
            
            if buttonIndex < actions.count {
                continuation.resume(returning: actions[buttonIndex])
            } else {
                continuation.resume(returning: nil)
            }
        }
    }
    
    func clearAllNotifications() {
        UNUserNotificationCenter.current().removeAllPendingNotificationRequests()
        UNUserNotificationCenter.current().removeAllDeliveredNotifications()
    }
    
    func clearNotification(withIdentifier identifier: String) {
        UNUserNotificationCenter.current().removePendingNotificationRequests(withIdentifiers: [identifier])
        UNUserNotificationCenter.current().removeDeliveredNotifications(withIdentifiers: [identifier])
    }
    
    // MARK: - UNUserNotificationCenterDelegate
    
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        willPresent notification: UNNotification,
        withCompletionHandler completionHandler: @escaping (UNNotificationPresentationOptions) -> Void
    ) {
        // Show notification even when app is in foreground
        completionHandler([.banner, .sound, .badge])
    }
    
    func userNotificationCenter(
        _ center: UNUserNotificationCenter,
        didReceive response: UNNotificationResponse,
        withCompletionHandler completionHandler: @escaping () -> Void
    ) {
        // Handle notification actions
        let identifier = response.notification.request.identifier
        print("Notification tapped: \(identifier)")
        
        // Bring app to front
        NSApp.activate(ignoringOtherApps: true)
        
        completionHandler()
    }
}