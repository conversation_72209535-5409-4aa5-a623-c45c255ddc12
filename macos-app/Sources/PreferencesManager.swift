import Foundation
import KeychainAccess

class PreferencesManager {
    private let userDefaults = UserDefaults.standard
    private let keychain = Keychain(service: "com.wowmonitor.macos")
    
    private enum Keys {
        static let settings = "app_settings"
        static let apiKey = "api_key"
        static let windowFrame = "window_frame"
        static let sidebarWidth = "sidebar_width"
        static let selectedView = "selected_view"
    }
    
    // MARK: - Settings Management
    
    func saveSettings(_ settings: AppSettings) {
        do {
            let data = try JSONEncoder().encode(settings)
            userDefaults.set(data, forKey: Keys.settings)
            
            // Save API key to keychain for security
            if let apiKey = settings.connection.apiKey {
                try keychain.set(apiKey, key: Keys.apiKey)
            } else {
                try keychain.remove(Keys.apiKey)
            }
        } catch {
            print("Failed to save settings: \(error)")
        }
    }
    
    func loadSettings() -> AppSettings? {
        guard let data = userDefaults.data(forKey: Keys.settings) else {
            return nil
        }
        
        do {
            var settings = try JSONDecoder().decode(AppSettings.self, from: data)
            
            // Load API key from keychain
            if let apiKey = try? keychain.get(Keys.apiKey) {
                settings.connection.apiKey = apiKey
            }
            
            return settings
        } catch {
            print("Failed to load settings: \(error)")
            return nil
        }
    }
    
    func resetSettings() {
        userDefaults.removeObject(forKey: Keys.settings)
        try? keychain.remove(Keys.apiKey)
    }
    
    // MARK: - Window State
    
    func saveWindowFrame(_ frame: CGRect) {
        let frameData = [
            "x": frame.origin.x,
            "y": frame.origin.y,
            "width": frame.size.width,
            "height": frame.size.height
        ]
        userDefaults.set(frameData, forKey: Keys.windowFrame)
    }
    
    func loadWindowFrame() -> CGRect? {
        guard let frameData = userDefaults.dictionary(forKey: Keys.windowFrame),
              let x = frameData["x"] as? CGFloat,
              let y = frameData["y"] as? CGFloat,
              let width = frameData["width"] as? CGFloat,
              let height = frameData["height"] as? CGFloat else {
            return nil
        }
        
        return CGRect(x: x, y: y, width: width, height: height)
    }
    
    func saveSidebarWidth(_ width: CGFloat) {
        userDefaults.set(width, forKey: Keys.sidebarWidth)
    }
    
    func loadSidebarWidth() -> CGFloat? {
        let width = userDefaults.double(forKey: Keys.sidebarWidth)
        return width > 0 ? width : nil
    }
    
    // MARK: - View State
    
    func saveSelectedView(_ view: String) {
        userDefaults.set(view, forKey: Keys.selectedView)
    }
    
    func loadSelectedView() -> String? {
        return userDefaults.string(forKey: Keys.selectedView)
    }
    
    // MARK: - Export/Import
    
    func exportSettings() -> Data? {
        guard let settings = loadSettings() else { return nil }
        
        // Create exportable settings (without sensitive data)
        var exportableSettings = settings
        exportableSettings.connection.apiKey = nil // Remove API key for security
        
        do {
            return try JSONEncoder().encode(exportableSettings)
        } catch {
            print("Failed to export settings: \(error)")
            return nil
        }
    }
    
    func importSettings(from data: Data) -> Bool {
        do {
            let settings = try JSONDecoder().decode(AppSettings.self, from: data)
            saveSettings(settings)
            return true
        } catch {
            print("Failed to import settings: \(error)")
            return false
        }
    }
    
    // MARK: - Utility Methods
    
    func clearAllData() {
        resetSettings()
        userDefaults.removeObject(forKey: Keys.windowFrame)
        userDefaults.removeObject(forKey: Keys.sidebarWidth)
        userDefaults.removeObject(forKey: Keys.selectedView)
    }
}