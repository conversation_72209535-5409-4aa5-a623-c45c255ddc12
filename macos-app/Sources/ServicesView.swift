import SwiftUI

struct ServicesView: View {
    @EnvironmentObject var appState: AppState
    @State private var searchText = ""
    @State private var selectedService: ServiceInfo?
    @State private var showingServiceDetail = false
    
    var body: some View {
        VStack(spacing: 0) {
            // Header with Search
            VStack(spacing: 12) {
                HStack {
                    Text("Services")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Spacer()
                    
                    Button("Refresh") {
                        Task {
                            await appState.refreshAllData()
                        }
                    }
                    .buttonStyle(.bordered)
                }
                
                HStack {
                    Image(systemName: "magnifyingglass")
                        .foregroundColor(.secondary)
                    
                    TextField("Search services...", text: $searchText)
                        .textFieldStyle(.roundedBorder)
                    
                    Spacer()
                    
                    ServiceStatusFilter()
                }
            }
            .padding()
            .background(Color(NSColor.windowBackgroundColor))
            
            Divider()
            
            // Services List
            if filteredServices.isEmpty {
                VStack(spacing: 16) {
                    Image(systemName: "gear.circle")
                        .font(.system(size: 64))
                        .foregroundColor(.secondary)
                    
                    Text(appState.services.isEmpty ? "No services found" : "No services match your search")
                        .font(.headline)
                        .foregroundColor(.secondary)
                    
                    if !appState.isConnected {
                        Button("Connect to Server") {
                            Task {
                                await appState.connectToServer()
                            }
                        }
                        .buttonStyle(.borderedProminent)
                    }
                }
                .frame(maxWidth: .infinity, maxHeight: .infinity)
            } else {
                List(filteredServices) { service in
                    ServiceRowView(service: service)
                        .onTapGesture {
                            selectedService = service
                            showingServiceDetail = true
                        }
                }
                .listStyle(.plain)
            }
        }
        .sheet(isPresented: $showingServiceDetail) {
            if let service = selectedService {
                ServiceDetailView(service: service)
            }
        }
        .refreshable {
            await appState.refreshAllData()
        }
    }
    
    private var filteredServices: [ServiceInfo] {
        if searchText.isEmpty {
            return appState.services
        }
        return appState.services.filter { service in
            service.name.localizedCaseInsensitiveContains(searchText) ||
            service.description?.localizedCaseInsensitiveContains(searchText) == true
        }
    }
}

struct ServiceRowView: View {
    let service: ServiceInfo
    @EnvironmentObject var appState: AppState
    @State private var isPerformingAction = false
    
    var body: some View {
        HStack(spacing: 16) {
            // Status Indicator
            VStack(spacing: 4) {
                Image(systemName: service.status.systemImage)
                    .font(.title2)
                    .foregroundColor(service.status.color)
                
                Text(service.status.rawValue.capitalized)
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            .frame(width: 80)
            
            // Service Information
            VStack(alignment: .leading, spacing: 6) {
                HStack {
                    Text(service.name)
                        .font(.headline)
                        .fontWeight(.semibold)
                    
                    if let version = service.version {
                        Text("v\(version)")
                            .font(.caption)
                            .foregroundColor(.secondary)
                            .padding(.horizontal, 6)
                            .padding(.vertical, 2)
                            .background(Color.secondary.opacity(0.1))
                            .clipShape(Capsule())
                    }
                    
                    Spacer()
                    
                    if let port = service.port {
                        Text(":\(port)")
                            .font(.caption)
                            .fontFamily(.monospaced)
                            .foregroundColor(.secondary)
                    }
                }
                
                if let description = service.description {
                    Text(description)
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                        .lineLimit(2)
                }
                
                HStack {
                    if let uptime = service.uptime {
                        Label(formatUptime(uptime), systemImage: "clock")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                    
                    if let resourceUsage = service.resourceUsage {
                        Label("\(String(format: "%.1f", resourceUsage.cpu))% CPU", systemImage: "cpu")
                            .font(.caption)
                            .foregroundColor(.secondary)
                    }
                }
            }
            
            Spacer()
            
            // Action Buttons
            HStack(spacing: 8) {
                if service.status == .running {
                    Button("Stop") {
                        performAction {
                            await appState.stopService(service.id)
                        }
                    }
                    .buttonStyle(.bordered)
                    
                    Button("Restart") {
                        performAction {
                            await appState.restartService(service.id)
                        }
                    }
                    .buttonStyle(.borderedProminent)
                } else if service.status == .stopped {
                    Button("Start") {
                        performAction {
                            await appState.startService(service.id)
                        }
                    }
                    .buttonStyle(.borderedProminent)
                }
            }
            .disabled(isPerformingAction)
            .opacity(isPerformingAction ? 0.6 : 1.0)
        }
        .padding(.vertical, 8)
        .contentShape(Rectangle())
    }
    
    private func performAction(_ action: @escaping () async -> Void) {
        isPerformingAction = true
        Task {
            await action()
            isPerformingAction = false
        }
    }
    
    private func formatUptime(_ uptime: TimeInterval) -> String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.day, .hour, .minute]
        formatter.unitsStyle = .abbreviated
        return formatter.string(from: uptime) ?? "Unknown"
    }
}

struct ServiceStatusFilter: View {
    // Placeholder for service status filtering
    var body: some View {
        Menu("All Services") {
            Button("All Services") { }
            Button("Running") { }
            Button("Stopped") { }
            Button("Error") { }
        }
        .menuStyle(.borderlessButton)
    }
}

struct ServiceDetailView: View {
    let service: ServiceInfo
    @Environment(\.dismiss) private var dismiss
    
    var body: some View {
        VStack(alignment: .leading, spacing: 20) {
            // Header
            HStack {
                VStack(alignment: .leading, spacing: 8) {
                    Text(service.name)
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    HStack(spacing: 12) {
                        Label(service.status.rawValue.capitalized, systemImage: service.status.systemImage)
                            .foregroundColor(service.status.color)
                        
                        if let version = service.version {
                            Label("Version \(version)", systemImage: "number")
                                .foregroundColor(.secondary)
                        }
                        
                        if let port = service.port {
                            Label("Port \(port)", systemImage: "network")
                                .foregroundColor(.secondary)
                        }
                    }
                    .font(.subheadline)
                }
                
                Spacer()
                
                Button("Close") {
                    dismiss()
                }
                .buttonStyle(.bordered)
            }
            
            // Service Details
            VStack(alignment: .leading, spacing: 16) {
                if let description = service.description {
                    DetailSection(title: "Description") {
                        Text(description)
                            .foregroundColor(.secondary)
                    }
                }
                
                if let uptime = service.uptime {
                    DetailSection(title: "Uptime") {
                        Text(formatUptime(uptime))
                            .fontFamily(.monospaced)
                    }
                }
                
                if !service.dependencies.isEmpty {
                    DetailSection(title: "Dependencies") {
                        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 3), spacing: 8) {
                            ForEach(service.dependencies, id: \.self) { dependency in
                                Text(dependency)
                                    .font(.caption)
                                    .padding(.horizontal, 8)
                                    .padding(.vertical, 4)
                                    .background(Color.secondary.opacity(0.1))
                                    .clipShape(Capsule())
                            }
                        }
                    }
                }
                
                if let resourceUsage = service.resourceUsage {
                    DetailSection(title: "Resource Usage") {
                        VStack(spacing: 8) {
                            ResourceUsageRow(label: "CPU", value: "\(String(format: "%.1f", resourceUsage.cpu))%")
                            ResourceUsageRow(label: "Memory", value: formatBytes(resourceUsage.memory))
                            ResourceUsageRow(label: "Disk Read", value: formatBytes(resourceUsage.diskRead))
                            ResourceUsageRow(label: "Disk Write", value: formatBytes(resourceUsage.diskWrite))
                            ResourceUsageRow(label: "Network In", value: formatBytes(resourceUsage.networkIn))
                            ResourceUsageRow(label: "Network Out", value: formatBytes(resourceUsage.networkOut))
                        }
                    }
                }
            }
            
            Spacer()
        }
        .padding()
        .frame(width: 500, height: 600)
    }
    
    private func formatUptime(_ uptime: TimeInterval) -> String {
        let formatter = DateComponentsFormatter()
        formatter.allowedUnits = [.day, .hour, .minute, .second]
        formatter.unitsStyle = .full
        return formatter.string(from: uptime) ?? "Unknown"
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .binary
        return formatter.string(fromByteCount: bytes)
    }
}

struct DetailSection<Content: View>: View {
    let title: String
    let content: Content
    
    init(title: String, @ViewBuilder content: () -> Content) {
        self.title = title
        self.content = content()
    }
    
    var body: some View {
        VStack(alignment: .leading, spacing: 8) {
            Text(title)
                .font(.headline)
                .fontWeight(.semibold)
            
            content
        }
    }
}

struct ResourceUsageRow: View {
    let label: String
    let value: String
    
    var body: some View {
        HStack {
            Text(label)
                .foregroundColor(.secondary)
            
            Spacer()
            
            Text(value)
                .fontFamily(.monospaced)
        }
        .font(.subheadline)
    }
}

#Preview {
    ServicesView()
        .environmentObject(AppState())
        .frame(width: 800, height: 600)
}