import SwiftUI
import DGCharts

struct DashboardView: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        ScrollView {
            LazyVStack(spacing: 20) {
                // Welcome Header
                DashboardHeader()
                
                // Quick Stats Cards
                QuickStatsGrid()
                
                // System Metrics Charts
                if appState.systemMetrics != nil {
                    SystemMetricsSection()
                }
                
                // Services & Docker Overview
                HStack(alignment: .top, spacing: 20) {
                    ServicesOverviewCard()
                    DockerOverviewCard()
                }
                
                // Recent Notifications
                RecentNotificationsCard()
            }
            .padding()
        }
        .background(Color(NSColor.controlBackgroundColor))
        .navigationTitle("Dashboard")
        .refreshable {
            await appState.refreshAllData()
        }
    }
}

// MARK: - Dashboard Header

struct DashboardHeader: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        VStack(spacing: 12) {
            HStack {
                VStack(alignment: .leading, spacing: 4) {
                    Text("WOW Monitor Dashboard")
                        .font(.largeTitle)
                        .fontWeight(.bold)
                    
                    Text("System monitoring and management")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                
                Spacer()
                
                // Connection Status Badge
                HStack(spacing: 8) {
                    Circle()
                        .fill(connectionStatusColor)
                        .frame(width: 12, height: 12)
                    
                    Text(connectionStatusText)
                        .font(.headline)
                        .fontWeight(.medium)
                }
                .padding(.horizontal, 16)
                .padding(.vertical, 8)
                .background(Color(NSColor.windowBackgroundColor))
                .clipShape(Capsule())
                .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
            }
            
            // Quick Actions
            HStack(spacing: 12) {
                QuickActionCard(
                    title: "Refresh All",
                    icon: "arrow.clockwise",
                    color: .blue,
                    action: {
                        Task {
                            await appState.refreshAllData()
                        }
                    }
                )
                
                QuickActionCard(
                    title: "Services",
                    icon: "gear.circle",
                    color: .green,
                    action: {
                        appState.currentView = .services
                    }
                )
                
                QuickActionCard(
                    title: "Docker",
                    icon: "shippingbox",
                    color: .orange,
                    action: {
                        appState.currentView = .docker
                    }
                )
                
                QuickActionCard(
                    title: "Logs",
                    icon: "doc.text",
                    color: .purple,
                    action: {
                        appState.currentView = .logs
                    }
                )
            }
        }
        .padding(20)
        .background(Color(NSColor.windowBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
    
    private var connectionStatusColor: Color {
        switch appState.connectionStatus {
        case .connected: return .green
        case .connecting: return .orange
        case .error: return .red
        case .disconnected: return .gray
        }
    }
    
    private var connectionStatusText: String {
        switch appState.connectionStatus {
        case .connected: return "Connected"
        case .connecting: return "Connecting..."
        case .error: return "Connection Error"
        case .disconnected: return "Disconnected"
        }
    }
}

// MARK: - Quick Stats Grid

struct QuickStatsGrid: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        LazyVGrid(columns: Array(repeating: GridItem(.flexible()), count: 4), spacing: 16) {
            if let metrics = appState.systemMetrics {
                StatCard(
                    title: "CPU Usage",
                    value: "\(String(format: "%.1f", metrics.cpu.usage))%",
                    icon: "cpu",
                    color: cpuColor(usage: metrics.cpu.usage),
                    trend: .stable
                )
                
                StatCard(
                    title: "Memory",
                    value: "\(String(format: "%.1f", metrics.memory.usagePercentage))%",
                    icon: "memorychip",
                    color: memoryColor(usage: metrics.memory.usagePercentage),
                    trend: .stable
                )
                
                StatCard(
                    title: "Disk Usage",
                    value: "\(String(format: "%.1f", metrics.disk.usagePercentage))%",
                    icon: "internaldrive",
                    color: diskColor(usage: metrics.disk.usagePercentage),
                    trend: .stable
                )
                
                StatCard(
                    title: "Network I/O",
                    value: formatBytes(metrics.network.bytesReceived + metrics.network.bytesSent),
                    icon: "network",
                    color: .blue,
                    trend: .up
                )
            } else {
                ForEach(0..<4, id: \.self) { _ in
                    StatCard(
                        title: "Loading...",
                        value: "--",
                        icon: "questionmark",
                        color: .gray,
                        trend: .stable
                    )
                    .redacted(reason: .placeholder)
                }
            }
        }
    }
    
    private func cpuColor(usage: Double) -> Color {
        if usage > 90 { return .red }
        if usage > 70 { return .orange }
        return .green
    }
    
    private func memoryColor(usage: Double) -> Color {
        if usage > 95 { return .red }
        if usage > 80 { return .orange }
        return .green
    }
    
    private func diskColor(usage: Double) -> Color {
        if usage > 95 { return .red }
        if usage > 85 { return .orange }
        return .green
    }
    
    private func formatBytes(_ bytes: Int64) -> String {
        let formatter = ByteCountFormatter()
        formatter.countStyle = .binary
        return formatter.string(fromByteCount: bytes)
    }
}

// MARK: - Stat Card

struct StatCard: View {
    let title: String
    let value: String
    let icon: String
    let color: Color
    let trend: TrendDirection
    
    var body: some View {
        VStack(alignment: .leading, spacing: 12) {
            HStack {
                Image(systemName: icon)
                    .foregroundColor(color)
                    .font(.title2)
                
                Spacer()
                
                Image(systemName: trend.iconName)
                    .foregroundColor(trend.color)
                    .font(.caption)
            }
            
            VStack(alignment: .leading, spacing: 4) {
                Text(value)
                    .font(.title)
                    .fontWeight(.bold)
                    .foregroundColor(.primary)
                
                Text(title)
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
        .padding(16)
        .background(Color(NSColor.windowBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: .black.opacity(0.1), radius: 2, x: 0, y: 1)
    }
}

enum TrendDirection {
    case up, down, stable
    
    var iconName: String {
        switch self {
        case .up: return "arrow.up.right"
        case .down: return "arrow.down.right"
        case .stable: return "minus"
        }
    }
    
    var color: Color {
        switch self {
        case .up: return .green
        case .down: return .red
        case .stable: return .gray
        }
    }
}

// MARK: - Quick Action Card

struct QuickActionCard: View {
    let title: String
    let icon: String
    let color: Color
    let action: () -> Void
    
    var body: some View {
        Button(action: action) {
            VStack(spacing: 8) {
                Image(systemName: icon)
                    .font(.title2)
                    .foregroundColor(color)
                
                Text(title)
                    .font(.caption)
                    .fontWeight(.medium)
                    .foregroundColor(.primary)
            }
            .frame(height: 60)
            .frame(maxWidth: .infinity)
            .background(Color(NSColor.windowBackgroundColor))
            .clipShape(RoundedRectangle(cornerRadius: 8))
            .shadow(color: .black.opacity(0.1), radius: 1, x: 0, y: 1)
        }
        .buttonStyle(.plain)
    }
}

// MARK: - System Metrics Section

struct SystemMetricsSection: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            Text("System Performance")
                .font(.title2)
                .fontWeight(.semibold)
            
            // Placeholder for charts - would implement with DGCharts
            HStack(spacing: 16) {
                ChartPlaceholder(title: "CPU Usage Over Time")
                ChartPlaceholder(title: "Memory Usage Over Time")
                ChartPlaceholder(title: "Network Activity")
            }
        }
        .padding(20)
        .background(Color(NSColor.windowBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct ChartPlaceholder: View {
    let title: String
    
    var body: some View {
        VStack {
            Text(title)
                .font(.headline)
                .padding(.bottom, 8)
            
            Rectangle()
                .fill(Color.accentColor.opacity(0.2))
                .frame(height: 120)
                .overlay(
                    Text("Chart Coming Soon")
                        .foregroundColor(.secondary)
                )
        }
        .padding()
        .background(Color(NSColor.controlBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 8))
    }
}

// MARK: - Services Overview Card

struct ServicesOverviewCard: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "gear.circle")
                    .foregroundColor(.green)
                    .font(.title2)
                
                Text("Services")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("View All") {
                    appState.currentView = .services
                }
                .font(.caption)
            }
            
            VStack(spacing: 12) {
                ServiceStatusRow(
                    status: .running,
                    count: appState.services.filter { $0.status == .running }.count,
                    total: appState.services.count
                )
                
                ServiceStatusRow(
                    status: .stopped,
                    count: appState.services.filter { $0.status == .stopped }.count,
                    total: appState.services.count
                )
                
                ServiceStatusRow(
                    status: .error,
                    count: appState.services.filter { $0.status == .error }.count,
                    total: appState.services.count
                )
            }
        }
        .padding(20)
        .background(Color(NSColor.windowBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct ServiceStatusRow: View {
    let status: ServiceStatus
    let count: Int
    let total: Int
    
    var body: some View {
        HStack {
            Image(systemName: status.systemImage)
                .foregroundColor(status.color)
                .frame(width: 16)
            
            Text(status.rawValue.capitalized)
                .font(.subheadline)
            
            Spacer()
            
            Text("\(count)")
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            if total > 0 {
                Text("(\(String(format: "%.0f", Double(count) / Double(total) * 100))%)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

// MARK: - Docker Overview Card

struct DockerOverviewCard: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "shippingbox")
                    .foregroundColor(.orange)
                    .font(.title2)
                
                Text("Docker")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                Button("View All") {
                    appState.currentView = .docker
                }
                .font(.caption)
            }
            
            VStack(spacing: 12) {
                ContainerStatusRow(
                    status: .running,
                    count: appState.dockerContainers.filter { $0.status == .running }.count,
                    total: appState.dockerContainers.count
                )
                
                ContainerStatusRow(
                    status: .exited,
                    count: appState.dockerContainers.filter { $0.status == .exited }.count,
                    total: appState.dockerContainers.count
                )
                
                HStack {
                    Image(systemName: "externaldrive")
                        .foregroundColor(.blue)
                        .frame(width: 16)
                    
                    Text("Images")
                        .font(.subheadline)
                    
                    Spacer()
                    
                    Text("\(appState.dockerImages.count)")
                        .font(.subheadline)
                        .fontWeight(.semibold)
                        .foregroundColor(.primary)
                }
            }
        }
        .padding(20)
        .background(Color(NSColor.windowBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct ContainerStatusRow: View {
    let status: ContainerStatus
    let count: Int
    let total: Int
    
    var body: some View {
        HStack {
            Image(systemName: status.systemImage)
                .foregroundColor(status.color)
                .frame(width: 16)
            
            Text(status.rawValue.capitalized)
                .font(.subheadline)
            
            Spacer()
            
            Text("\(count)")
                .font(.subheadline)
                .fontWeight(.semibold)
                .foregroundColor(.primary)
            
            if total > 0 {
                Text("(\(String(format: "%.0f", Double(count) / Double(total) * 100))%)")
                    .font(.caption)
                    .foregroundColor(.secondary)
            }
        }
    }
}

// MARK: - Recent Notifications Card

struct RecentNotificationsCard: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        VStack(alignment: .leading, spacing: 16) {
            HStack {
                Image(systemName: "bell")
                    .foregroundColor(.purple)
                    .font(.title2)
                
                Text("Recent Notifications")
                    .font(.headline)
                    .fontWeight(.semibold)
                
                Spacer()
                
                if !appState.notifications.isEmpty {
                    Button("View All") {
                        appState.currentView = .notifications
                    }
                    .font(.caption)
                }
            }
            
            if appState.notifications.isEmpty {
                VStack(spacing: 8) {
                    Image(systemName: "bell.slash")
                        .font(.title)
                        .foregroundColor(.secondary)
                    
                    Text("No recent notifications")
                        .font(.subheadline)
                        .foregroundColor(.secondary)
                }
                .frame(maxWidth: .infinity)
                .padding(.vertical, 20)
            } else {
                LazyVStack(spacing: 12) {
                    ForEach(Array(appState.notifications.prefix(3))) { notification in
                        NotificationRow(notification: notification)
                    }
                }
            }
        }
        .padding(20)
        .background(Color(NSColor.windowBackgroundColor))
        .clipShape(RoundedRectangle(cornerRadius: 12))
        .shadow(color: .black.opacity(0.1), radius: 4, x: 0, y: 2)
    }
}

struct NotificationRow: View {
    let notification: AppNotification
    
    var body: some View {
        HStack(alignment: .top, spacing: 12) {
            Image(systemName: notification.type.systemImage)
                .foregroundColor(notification.type.color)
                .font(.subheadline)
            
            VStack(alignment: .leading, spacing: 4) {
                Text(notification.title)
                    .font(.subheadline)
                    .fontWeight(.medium)
                
                Text(notification.message)
                    .font(.caption)
                    .foregroundColor(.secondary)
                    .lineLimit(2)
                
                Text(notification.timestamp.formatted(.relative(presentation: .named)))
                    .font(.caption2)
                    .foregroundColor(.secondary)
            }
            
            Spacer()
            
            if !notification.read {
                Circle()
                    .fill(Color.accentColor)
                    .frame(width: 8, height: 8)
            }
        }
    }
}

#Preview {
    DashboardView()
        .environmentObject(AppState())
        .frame(width: 1000, height: 800)
}