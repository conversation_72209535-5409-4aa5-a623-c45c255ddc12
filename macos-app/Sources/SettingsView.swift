import SwiftUI

struct SettingsView: View {
    @EnvironmentObject var appState: AppState
    @State private var settings: AppSettings
    @State private var isModified = false
    
    init() {
        self._settings = State(initialValue: AppSettings(
            connection: ConnectionSettings(
                serverURL: "http://localhost:3000",
                apiKey: nil,
                websocketURL: "ws://localhost:3000",
                autoConnect: true,
                connectionTimeout: 30,
                retryAttempts: 3
            ),
            notifications: NotificationSettings(
                systemAlerts: true,
                serviceEvents: true,
                dockerEvents: true,
                errorAlerts: true,
                soundEnabled: true,
                badgeEnabled: true
            ),
            appearance: AppearanceSettings(
                colorScheme: .system,
                accentColor: "blue",
                showMenuBar: true,
                compactMode: false
            ),
            monitoring: MonitoringSettings(
                refreshInterval: 5.0,
                historyRetention: 86400,
                alertThresholds: AlertThresholds(
                    cpuWarning: 70.0,
                    cpuCritical: 90.0,
                    memoryWarning: 80.0,
                    memoryCritical: 95.0,
                    diskWarning: 85.0,
                    diskCritical: 95.0
                ),
                enableRealtime: true
            ),
            shortcuts: ShortcutSettings(
                quickSearch: "⌘K",
                refreshAll: "⌘R",
                connectServer: "⌘⇧C",
                checkUpdates: "⌘⇧U",
                showMenuBar: "⌘⇧M"
            ),
            advanced: AdvancedSettings(
                debugMode: false,
                logLevel: .info,
                enableAnalytics: false,
                exportFormat: .json
            )
        ))
    }
    
    var body: some View {
        TabView {
            // Connection Settings
            ConnectionSettingsView(settings: $settings.connection)
                .tabItem {
                    Label("Connection", systemImage: "network")
                }
            
            // Notification Settings
            NotificationSettingsView(settings: $settings.notifications)
                .tabItem {
                    Label("Notifications", systemImage: "bell")
                }
            
            // Appearance Settings
            AppearanceSettingsView(settings: $settings.appearance)
                .tabItem {
                    Label("Appearance", systemImage: "paintbrush")
                }
            
            // Monitoring Settings
            MonitoringSettingsView(settings: $settings.monitoring)
                .tabItem {
                    Label("Monitoring", systemImage: "chart.line.uptrend.xyaxis")
                }
            
            // Shortcuts Settings
            ShortcutSettingsView(settings: $settings.shortcuts)
                .tabItem {
                    Label("Shortcuts", systemImage: "keyboard")
                }
            
            // Advanced Settings
            AdvancedSettingsView(settings: $settings.advanced)
                .tabItem {
                    Label("Advanced", systemImage: "gearshape.2")
                }
        }
        .frame(width: 600, height: 500)
        .onAppear {
            settings = appState.settings
        }
        .onChange(of: settings) { _ in
            isModified = true
        }
        .toolbar {
            ToolbarItem(placement: .cancellationAction) {
                Button("Cancel") {
                    settings = appState.settings
                    isModified = false
                }
                .disabled(!isModified)
            }
            
            ToolbarItem(placement: .confirmationAction) {
                Button("Save") {
                    appState.updateSettings(settings)
                    isModified = false
                }
                .disabled(!isModified)
                .buttonStyle(.borderedProminent)
            }
        }
    }
}

// MARK: - Connection Settings

struct ConnectionSettingsView: View {
    @Binding var settings: ConnectionSettings
    
    var body: some View {
        Form {
            Section("Server Configuration") {
                TextField("Server URL", text: $settings.serverURL)
                    .help("The base URL of your WOW Monitor backend server")
                
                SecureField("API Key (Optional)", text: Binding(
                    get: { settings.apiKey ?? "" },
                    set: { settings.apiKey = $0.isEmpty ? nil : $0 }
                ))
                .help("API key for authentication if required")
                
                TextField("WebSocket URL", text: Binding(
                    get: { settings.websocketURL ?? "" },
                    set: { settings.websocketURL = $0.isEmpty ? nil : $0 }
                ))
                .help("WebSocket URL for real-time updates")
            }
            
            Section("Connection Options") {
                Toggle("Auto-connect on startup", isOn: $settings.autoConnect)
                
                Stepper("Connection timeout: \(Int(settings.connectionTimeout))s",
                       value: $settings.connectionTimeout,
                       in: 5...120,
                       step: 5)
                
                Stepper("Retry attempts: \(settings.retryAttempts)",
                       value: $settings.retryAttempts,
                       in: 1...10)
            }
        }
        .padding()
    }
}

// MARK: - Notification Settings

struct NotificationSettingsView: View {
    @Binding var settings: NotificationSettings
    
    var body: some View {
        Form {
            Section("Alert Types") {
                Toggle("System alerts (CPU, memory, disk)", isOn: $settings.systemAlerts)
                Toggle("Service events (start, stop, restart)", isOn: $settings.serviceEvents)
                Toggle("Docker events (container lifecycle)", isOn: $settings.dockerEvents)
                Toggle("Error alerts", isOn: $settings.errorAlerts)
            }
            
            Section("Notification Options") {
                Toggle("Sound notifications", isOn: $settings.soundEnabled)
                Toggle("Menu bar badge", isOn: $settings.badgeEnabled)
            }
        }
        .padding()
    }
}

// MARK: - Appearance Settings

struct AppearanceSettingsView: View {
    @Binding var settings: AppearanceSettings
    
    var body: some View {
        Form {
            Section("Theme") {
                Picker("Color scheme", selection: $settings.colorScheme) {
                    Text("System").tag(AppColorScheme.system)
                    Text("Light").tag(AppColorScheme.light)
                    Text("Dark").tag(AppColorScheme.dark)
                }
                .pickerStyle(.segmented)
                
                Picker("Accent color", selection: $settings.accentColor) {
                    Text("Blue").tag("blue")
                    Text("Purple").tag("purple")
                    Text("Pink").tag("pink")
                    Text("Red").tag("red")
                    Text("Orange").tag("orange")
                    Text("Yellow").tag("yellow")
                    Text("Green").tag("green")
                }
            }
            
            Section("Interface") {
                Toggle("Show menu bar extra", isOn: $settings.showMenuBar)
                Toggle("Compact mode", isOn: $settings.compactMode)
            }
        }
        .padding()
    }
}

// MARK: - Monitoring Settings

struct MonitoringSettingsView: View {
    @Binding var settings: MonitoringSettings
    
    var body: some View {
        Form {
            Section("Data Refresh") {
                Stepper("Refresh interval: \(String(format: "%.1f", settings.refreshInterval))s",
                       value: $settings.refreshInterval,
                       in: 1.0...60.0,
                       step: 0.5)
                
                Picker("History retention", selection: $settings.historyRetention) {
                    Text("1 hour").tag(TimeInterval(3600))
                    Text("6 hours").tag(TimeInterval(21600))
                    Text("12 hours").tag(TimeInterval(43200))
                    Text("24 hours").tag(TimeInterval(86400))
                    Text("7 days").tag(TimeInterval(604800))
                }
                
                Toggle("Enable real-time updates", isOn: $settings.enableRealtime)
            }
            
            Section("Alert Thresholds") {
                VStack(alignment: .leading, spacing: 8) {
                    Text("CPU Usage")
                        .font(.headline)
                    
                    HStack {
                        Text("Warning:")
                        Slider(value: $settings.alertThresholds.cpuWarning, in: 50...90, step: 5)
                        Text("\(Int(settings.alertThresholds.cpuWarning))%")
                            .frame(width: 40)
                    }
                    
                    HStack {
                        Text("Critical:")
                        Slider(value: $settings.alertThresholds.cpuCritical, in: 80...100, step: 5)
                        Text("\(Int(settings.alertThresholds.cpuCritical))%")
                            .frame(width: 40)
                    }
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Memory Usage")
                        .font(.headline)
                    
                    HStack {
                        Text("Warning:")
                        Slider(value: $settings.alertThresholds.memoryWarning, in: 60...90, step: 5)
                        Text("\(Int(settings.alertThresholds.memoryWarning))%")
                            .frame(width: 40)
                    }
                    
                    HStack {
                        Text("Critical:")
                        Slider(value: $settings.alertThresholds.memoryCritical, in: 90...100, step: 1)
                        Text("\(Int(settings.alertThresholds.memoryCritical))%")
                            .frame(width: 40)
                    }
                }
                
                VStack(alignment: .leading, spacing: 8) {
                    Text("Disk Usage")
                        .font(.headline)
                    
                    HStack {
                        Text("Warning:")
                        Slider(value: $settings.alertThresholds.diskWarning, in: 70...95, step: 5)
                        Text("\(Int(settings.alertThresholds.diskWarning))%")
                            .frame(width: 40)
                    }
                    
                    HStack {
                        Text("Critical:")
                        Slider(value: $settings.alertThresholds.diskCritical, in: 90...100, step: 1)
                        Text("\(Int(settings.alertThresholds.diskCritical))%")
                            .frame(width: 40)
                    }
                }
            }
        }
        .padding()
    }
}

// MARK: - Shortcuts Settings

struct ShortcutSettingsView: View {
    @Binding var settings: ShortcutSettings
    
    var body: some View {
        Form {
            Section("Keyboard Shortcuts") {
                HStack {
                    Text("Quick Search")
                    Spacer()
                    Text(settings.quickSearch)
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Text("Refresh All Data")
                    Spacer()
                    Text(settings.refreshAll)
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Text("Connect to Server")
                    Spacer()
                    Text(settings.connectServer)
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Text("Check for Updates")
                    Spacer()
                    Text(settings.checkUpdates)
                        .foregroundColor(.secondary)
                }
                
                HStack {
                    Text("Toggle Menu Bar")
                    Spacer()
                    Text(settings.showMenuBar)
                        .foregroundColor(.secondary)
                }
            }
            
            Text("Keyboard shortcuts cannot be customized in this version.")
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding()
    }
}

// MARK: - Advanced Settings

struct AdvancedSettingsView: View {
    @Binding var settings: AdvancedSettings
    
    var body: some View {
        Form {
            Section("Debug") {
                Toggle("Debug mode", isOn: $settings.debugMode)
                    .help("Enable debug logging and additional diagnostic information")
                
                Picker("Log level", selection: $settings.logLevel) {
                    Text("Debug").tag(LogLevel.debug)
                    Text("Info").tag(LogLevel.info)
                    Text("Warning").tag(LogLevel.warning)
                    Text("Error").tag(LogLevel.error)
                    Text("Critical").tag(LogLevel.critical)
                }
            }
            
            Section("Privacy") {
                Toggle("Enable analytics", isOn: $settings.enableAnalytics)
                    .help("Send anonymous usage data to help improve the application")
            }
            
            Section("Export") {
                Picker("Default export format", selection: $settings.exportFormat) {
                    Text("JSON").tag(ExportFormat.json)
                    Text("CSV").tag(ExportFormat.csv)
                    Text("PDF").tag(ExportFormat.pdf)
                }
            }
            
            Section("Data Management") {
                Button("Reset All Settings") {
                    // TODO: Implement reset functionality
                }
                .foregroundColor(.red)
                
                Button("Clear Cache") {
                    // TODO: Implement cache clearing
                }
                
                Button("Export Settings") {
                    // TODO: Implement settings export
                }
                
                Button("Import Settings") {
                    // TODO: Implement settings import
                }
            }
        }
        .padding()
    }
}

#Preview {
    SettingsView()
        .environmentObject(AppState())
}