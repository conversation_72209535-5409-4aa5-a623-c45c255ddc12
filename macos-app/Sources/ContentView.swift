import SwiftUI

struct ContentView: View {
    @EnvironmentObject var appState: AppState
    @State private var selectedSidebarItem: SidebarItem = .dashboard
    
    var body: some View {
        NavigationSplitView {
            // Sidebar
            SidebarView(selectedItem: $selectedSidebarItem)
                .navigationSplitViewColumnWidth(min: 200, ideal: 250, max: 300)
        } detail: {
            // Main Content
            Group {
                switch selectedSidebarItem {
                case .dashboard:
                    DashboardView()
                case .services:
                    ServicesView()
                case .docker:
                    DockerView()
                case .systemMetrics:
                    SystemMetricsView()
                case .logs:
                    LogsView()
                case .notifications:
                    NotificationsView()
                }
            }
            .frame(maxWidth: .infinity, maxHeight: .infinity)
            .background(Color(NSColor.controlBackgroundColor))
        }
        .overlay(alignment: .center) {
            if appState.showingQuickSearch {
                QuickSearchView()
                    .transition(.opacity.combined(with: .scale))
            }
        }
        .animation(.easeInOut(duration: 0.3), value: appState.showingQuickSearch)
        .onChange(of: selectedSidebarItem) { newValue in
            appState.currentView = newValue.appView
        }
        .onChange(of: appState.currentView) { newValue in
            selectedSidebarItem = SidebarItem.from(appView: newValue)
        }
        .toolbar {
            ToolbarItemGroup(placement: .primaryAction) {
                ConnectionStatusView()
                
                Button(action: {
                    Task {
                        await appState.refreshAllData()
                    }
                }) {
                    Image(systemName: "arrow.clockwise")
                }
                .help("Refresh all data")
                
                Button(action: {
                    appState.showingQuickSearch = true
                }) {
                    Image(systemName: "magnifyingglass")
                }
                .help("Quick Search (⌘K)")
            }
        }
    }
}

struct SidebarView: View {
    @Binding var selectedItem: SidebarItem
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        List(selection: $selectedItem) {
            Section("Monitoring") {
                SidebarItemView(
                    item: .dashboard,
                    isSelected: selectedItem == .dashboard
                )
                
                SidebarItemView(
                    item: .systemMetrics,
                    isSelected: selectedItem == .systemMetrics
                )
            }
            
            Section("Management") {
                SidebarItemView(
                    item: .services,
                    isSelected: selectedItem == .services,
                    badge: appState.services.filter { $0.status == .running }.count
                )
                
                SidebarItemView(
                    item: .docker,
                    isSelected: selectedItem == .docker,
                    badge: appState.dockerContainers.filter { $0.status == .running }.count
                )
            }
            
            Section("Diagnostics") {
                SidebarItemView(
                    item: .logs,
                    isSelected: selectedItem == .logs
                )
                
                SidebarItemView(
                    item: .notifications,
                    isSelected: selectedItem == .notifications,
                    badge: appState.notifications.filter { !$0.read }.count
                )
            }
        }
        .listStyle(.sidebar)
        .navigationTitle("WOW Monitor")
    }
}

struct SidebarItemView: View {
    let item: SidebarItem
    let isSelected: Bool
    let badge: Int?
    
    init(item: SidebarItem, isSelected: Bool, badge: Int? = nil) {
        self.item = item
        self.isSelected = isSelected
        self.badge = badge
    }
    
    var body: some View {
        Label {
            HStack {
                Text(item.title)
                Spacer()
                if let badge = badge, badge > 0 {
                    Text("\(badge)")
                        .font(.caption)
                        .foregroundColor(.white)
                        .padding(.horizontal, 6)
                        .padding(.vertical, 2)
                        .background(Color.accentColor)
                        .clipShape(Capsule())
                }
            }
        } icon: {
            Image(systemName: item.iconName)
                .foregroundColor(isSelected ? .accentColor : .secondary)
        }
        .tag(item)
    }
}

struct ConnectionStatusView: View {
    @EnvironmentObject var appState: AppState
    
    var body: some View {
        HStack(spacing: 6) {
            Circle()
                .fill(statusColor)
                .frame(width: 8, height: 8)
            
            Text(statusText)
                .font(.caption)
                .foregroundColor(.secondary)
        }
        .padding(.horizontal, 8)
        .padding(.vertical, 4)
        .background(Color(NSColor.controlBackgroundColor))
        .clipShape(Capsule())
        .onTapGesture {
            if !appState.isConnected {
                Task {
                    await appState.connectToServer()
                }
            }
        }
        .help(appState.isConnected ? "Connected to server" : "Tap to connect")
    }
    
    private var statusColor: Color {
        switch appState.connectionStatus {
        case .connected:
            return .green
        case .connecting:
            return .orange
        case .error:
            return .red
        case .disconnected:
            return .gray
        }
    }
    
    private var statusText: String {
        switch appState.connectionStatus {
        case .connected:
            return "Connected"
        case .connecting:
            return "Connecting..."
        case .error:
            return "Error"
        case .disconnected:
            return "Disconnected"
        }
    }
}

// MARK: - Supporting Types

enum SidebarItem: String, CaseIterable, Identifiable {
    case dashboard = "dashboard"
    case services = "services"
    case docker = "docker"
    case systemMetrics = "systemMetrics"
    case logs = "logs"
    case notifications = "notifications"
    
    var id: String { rawValue }
    
    var title: String {
        switch self {
        case .dashboard:
            return "Dashboard"
        case .services:
            return "Services"
        case .docker:
            return "Docker"
        case .systemMetrics:
            return "System Metrics"
        case .logs:
            return "Logs"
        case .notifications:
            return "Notifications"
        }
    }
    
    var iconName: String {
        switch self {
        case .dashboard:
            return "rectangle.3.group"
        case .services:
            return "gear.circle"
        case .docker:
            return "shippingbox"
        case .systemMetrics:
            return "chart.line.uptrend.xyaxis"
        case .logs:
            return "doc.text"
        case .notifications:
            return "bell"
        }
    }
    
    var appView: AppView {
        switch self {
        case .dashboard:
            return .dashboard
        case .services:
            return .services
        case .docker:
            return .docker
        case .systemMetrics:
            return .systemMetrics
        case .logs:
            return .logs
        case .notifications:
            return .notifications
        }
    }
    
    static func from(appView: AppView) -> SidebarItem {
        switch appView {
        case .dashboard:
            return .dashboard
        case .services:
            return .services
        case .docker:
            return .docker
        case .systemMetrics:
            return .systemMetrics
        case .logs:
            return .logs
        case .notifications:
            return .notifications
        case .settings:
            return .dashboard // Default fallback
        }
    }
}

#Preview {
    ContentView()
        .environmentObject(AppState())
        .frame(width: 1200, height: 800)
}