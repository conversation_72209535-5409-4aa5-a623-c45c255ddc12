#!/bin/bash

# WOW Monitor macOS Build Script
# This script builds and runs the WOW Monitor macOS application

set -e  # Exit on any error

echo "🏗️  Building WOW Monitor macOS Application..."

# Check if we're in the correct directory
if [ ! -f "Package.swift" ]; then
    echo "❌ Error: Package.swift not found. Please run this script from the macos-app directory."
    exit 1
fi

# Clean previous builds
echo "🧹 Cleaning previous builds..."
rm -rf .build/

# Resolve dependencies
echo "📦 Resolving dependencies..."
swift package resolve

# Build the application
echo "🔨 Building application..."
swift build -c release

# Check if build was successful
if [ $? -eq 0 ]; then
    echo "✅ Build successful!"
    
    # Ask if user wants to run the application
    read -p "🚀 Would you like to run the application now? (y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Yy]$ ]]; then
        echo "🚀 Starting WOW Monitor..."
        swift run -c release
    fi
else
    echo "❌ Build failed!"
    exit 1
fi