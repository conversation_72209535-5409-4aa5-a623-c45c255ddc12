# WOW Monitor macOS App - Implementation Guide

This guide provides instructions for building, running, and developing the WOW Monitor macOS application.

## 🎯 Overview

The WOW Monitor macOS app is a native SwiftUI application that provides a comprehensive interface for monitoring and managing your WOW Monitor backend infrastructure. It features real-time system monitoring, service management, Docker container control, and much more.

## 📋 Prerequisites

Before you begin, ensure you have the following installed:

- **macOS**: 13.0 (Ventura) or later
- **Xcode**: 15.0 or later
- **Swift**: 5.9 or later
- **WOW Monitor Backend**: Running and accessible

## 🚀 Quick Start

### 1. Build and Run

The easiest way to get started is using the provided build script:

```bash
cd macos-app
./build.sh
```

This script will:
- Clean previous builds
- Resolve Swift package dependencies
- Build the application in release mode
- Optionally run the application

### 2. Alternative Build Methods

#### Using Swift Package Manager:
```bash
# Build only
swift build -c release

# Build and run
swift run -c release

# Development build (debug mode)
swift build
swift run
```

#### Using Xcode:
```bash
# Open the package in Xcode
open Package.swift

# Then build and run using X<PERSON>'s interface (⌘R)
```

## 🏗️ Architecture Overview

The application follows a modern SwiftUI MVVM architecture:

```
Sources/
├── App.swift                    # Main application entry point
├── AppState.swift              # Global state management (@MainActor)
├── Models.swift                # Core data models and types
├── ContentView.swift           # Main application layout with navigation
│
├── Views/                      # UI Components
│   ├── DashboardView.swift     # System overview dashboard
│   ├── ServicesView.swift      # Service management interface
│   ├── DockerView.swift        # Docker container management
│   ├── SystemMetricsView.swift # System performance metrics
│   ├── LogsView.swift          # Log viewing and filtering
│   ├── NotificationsView.swift # Notification management
│   ├── SettingsView.swift      # Application preferences
│   ├── MenuBarView.swift       # Menu bar interface
│   └── QuickSearchView.swift   # Global search overlay
│
├── Managers/                   # Service Layer
│   ├── APIClient.swift         # Backend API communication
│   ├── SocketManager.swift     # WebSocket real-time updates
│   ├── NotificationManager.swift # Native notifications
│   └── PreferencesManager.swift  # Settings persistence
│
└── Package.swift               # Swift Package configuration
```

### Key Components

#### 1. AppState (@MainActor)
- Centralized state management using `@StateObject` and `@ObservableObject`
- Handles API communication, WebSocket connections, and data synchronization
- Manages UI state, navigation, and user interactions

#### 2. API Client
- HTTP-based communication with the WOW Monitor backend
- Uses Alamofire for robust networking
- Handles authentication, retries, and error handling

#### 3. Socket Manager
- Real-time communication via WebSocket using Starscream
- Automatic reconnection and connection management
- Live updates for system metrics, service status, and logs

#### 4. Views
- Pure SwiftUI views following Apple's design principles
- Responsive layout supporting different window sizes
- Native macOS integration (menu bar, notifications, shortcuts)

## 🔧 Configuration

### Backend Connection

On first launch, configure your backend connection:

1. **Server URL**: Your WOW Monitor backend URL (e.g., `http://localhost:3000`)
2. **API Key**: Authentication token (if required by your backend)
3. **WebSocket URL**: Real-time connection endpoint (e.g., `ws://localhost:3000`)

### Settings Categories

The app provides comprehensive settings across multiple categories:

- **Connection**: Server URLs, authentication, connection timeout
- **Notifications**: Alert preferences, sound settings, notification types
- **Appearance**: Theme, colors, menu bar integration
- **Monitoring**: Refresh intervals, alert thresholds, real-time updates
- **Shortcuts**: Keyboard shortcuts (view-only in current version)
- **Advanced**: Debug mode, logging, export formats

## 🔍 Features Implemented

### ✅ Core Features
- [x] Real-time dashboard with system overview
- [x] Service management (start, stop, restart)
- [x] Docker container management and monitoring
- [x] System metrics display (CPU, memory, disk, network)
- [x] Log viewing with filtering and search
- [x] Notification system with native macOS integration
- [x] Menu bar integration for quick access
- [x] Global quick search functionality
- [x] Comprehensive settings management
- [x] WebSocket real-time updates
- [x] HTTP API communication

### ✅ UI/UX Features
- [x] Native macOS design following HIG
- [x] Dark/Light mode support
- [x] Responsive layout with resizable sidebar
- [x] Keyboard shortcuts
- [x] Context menus and swipe actions
- [x] Pull-to-refresh support
- [x] Loading states and error handling

### ✅ Advanced Features
- [x] Secure credential storage (Keychain)
- [x] Settings persistence and synchronization
- [x] Connection state management
- [x] Automatic reconnection
- [x] Memory-efficient data handling

## 🛠️ Development

### Adding New Features

1. **Create Models**: Define data structures in `Models.swift`
2. **Add API Methods**: Extend `APIClient.swift` for backend communication
3. **Create Views**: Build SwiftUI interfaces in the appropriate view files
4. **Update State**: Modify `AppState.swift` for global state management
5. **Add Navigation**: Update `ContentView.swift` for new sections

### Code Style Guidelines

- Follow Swift API Design Guidelines
- Use SwiftUI best practices and view composition
- Maintain consistent naming conventions
- Use `@MainActor` for UI-related state management
- Implement proper error handling and loading states

### Testing

Currently, the application focuses on UI implementation. For production use, consider adding:

- Unit tests for business logic
- UI tests for critical user flows
- Integration tests for API communication
- Performance tests for large datasets

## 🔗 Backend Integration

The app is designed to work with a REST API backend that provides:

### Required Endpoints

```
GET  /health                           # Connection test
GET  /api/system/metrics               # System metrics
GET  /api/services                     # Services list
POST /api/services/{id}/start          # Start service
POST /api/services/{id}/stop           # Stop service
POST /api/services/{id}/restart        # Restart service
GET  /api/docker/containers            # Docker containers
GET  /api/docker/images                # Docker images
POST /api/docker/containers/{id}/start # Start container
POST /api/docker/containers/{id}/stop  # Stop container
GET  /api/logs                         # Recent logs
```

### WebSocket Events

The app subscribes to real-time updates via WebSocket:

- `system_metrics`: Live system performance data
- `service_update`: Service status changes
- `docker_update`: Container status changes
- `log_entry`: New log entries
- `notification`: System notifications

## 🚨 Known Limitations

1. **Chart Visualization**: DGCharts integration is prepared but not fully implemented
2. **Keyboard Shortcuts**: Global shortcuts are defined but not customizable in settings
3. **Export Functionality**: Data export features are UI-ready but need backend integration
4. **Update Manager**: Auto-update system is structured but not implemented
5. **Advanced Filtering**: Some filter options in logs and services need backend support

## 🔮 Future Enhancements

### Planned Features
- [ ] Interactive charts and graphs for system metrics
- [ ] Advanced log analysis and filtering
- [ ] Bulk operations for services and containers
- [ ] Custom notification rules and thresholds
- [ ] Data export in multiple formats (JSON, CSV, PDF)
- [ ] Plugin system for extensibility
- [ ] Multi-server monitoring
- [ ] Historical data persistence and analysis

### Technical Improvements
- [ ] Comprehensive test suite
- [ ] Performance optimizations for large datasets
- [ ] Advanced caching strategies
- [ ] Background refresh capabilities
- [ ] Enhanced error recovery mechanisms

## 🐛 Troubleshooting

### Common Issues

**Build Failures**
- Ensure Xcode Command Line Tools are installed: `xcode-select --install`
- Clean build folder: `rm -rf .build && swift package clean`
- Update Swift toolchain if needed

**Connection Problems**
- Verify backend server is running and accessible
- Check firewall settings and network connectivity
- Validate server URLs in settings
- Review API key configuration

**Performance Issues**
- Reduce refresh interval in settings
- Disable real-time features if not needed
- Check available system memory
- Monitor WebSocket connection stability

### Debug Mode

Enable debug mode in Settings → Advanced for detailed logging and diagnostic information.

## 📝 Contributing

1. Fork the repository
2. Create a feature branch (`git checkout -b feature/amazing-feature`)
3. Make your changes following the code style guidelines
4. Test your changes thoroughly
5. Commit your changes (`git commit -m 'Add amazing feature'`)
6. Push to the branch (`git push origin feature/amazing-feature`)
7. Open a Pull Request

## 📄 License

This project is licensed under the MIT License. See the LICENSE file for details.

## 🆘 Support

- **Documentation**: Check the main README.md for comprehensive information
- **Issues**: Report bugs and request features through the issue tracker
- **Discussions**: Join community discussions for questions and ideas

---

**WOW Monitor macOS** - Professional infrastructure monitoring for macOS developers and system administrators.