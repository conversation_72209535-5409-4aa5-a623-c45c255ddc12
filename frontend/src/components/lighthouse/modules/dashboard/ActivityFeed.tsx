import React from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Badge } from '~/components/ui/badge';
import { Button } from '~/components/ui/button';
import { ScrollArea } from '~/components/ui/scroll-area';
import {
  Activity,
  FileText,
  Brain,
  Lightbulb,
  Bot,
  Link,
  CheckCircle,
  AlertCircle,
  Clock,
} from 'lucide-react';
import { cn } from '~/lib/utils';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';
import { formatDistanceToNow } from 'date-fns';

interface ActivityItem {
  id: string;
  type: 'insight' | 'agent' | 'knowledge' | 'source' | 'learning';
  title: string;
  description: string;
  timestamp: Date;
  status?: 'success' | 'pending' | 'error';
  icon: React.ElementType;
  module?: any;
}

export function ActivityFeed() {
  const {
    insights,
    activeAgents,
    agentHistory,
    learningEvents,
    knowledgeSources,
    currentProject,
    navigateToModule,
  } = useLighthouseStore();

  // Generate activity items from various sources
  const activities: ActivityItem[] = [];

  // Add recent insights
  insights.slice(0, 5).forEach((insight) => {
    activities.push({
      id: `insight-${insight.id}`,
      type: 'insight',
      title: 'New Insight Generated',
      description: insight.content.substring(0, 100) + '...',
      timestamp: new Date(insight.timestamp),
      status: 'success',
      icon: Lightbulb,
      module: 'insights',
    });
  });

  // Add agent activities
  [...activeAgents, ...agentHistory.slice(0, 3)].forEach((agent) => {
    activities.push({
      id: `agent-${agent.id}`,
      type: 'agent',
      title: `Agent: ${agent.name}`,
      description: agent.context.goal,
      timestamp: new Date(), // TODO: Add timestamp to agent
      status: agent.status === 'completed' ? 'success' : 
              agent.status === 'failed' ? 'error' : 'pending',
      icon: Bot,
      module: 'agents',
    });
  });

  // Add learning events
  learningEvents.slice(0, 3).forEach((event) => {
    const learningTypeLabel = {
      concept: 'New Concept',
      connection: 'Connection Found',
      pattern: 'Pattern Recognized',
      correction: 'Knowledge Corrected',
      reinforcement: 'Knowledge Reinforced',
      revision: 'Knowledge Revised'
    };
    
    activities.push({
      id: `learning-${event.id}`,
      type: 'learning',
      title: learningTypeLabel[event.type] || `Learning: ${event.type}`,
      description: event.after?.understanding || 'Knowledge updated',
      timestamp: new Date(event.timestamp),
      status: 'success',
      icon: Brain,
      module: 'knowledge',
    });
  });

  // Add recent sources
  knowledgeSources.slice(0, 3).forEach((source) => {
    activities.push({
      id: `source-${source.id}`,
      type: 'source',
      title: 'New Source Added',
      description: source.metadata.title || source.location,
      timestamp: new Date(), // TODO: Add timestamp to source
      status: 'success',
      icon: FileText,
      module: 'sources',
    });
  });

  // Sort by timestamp (ensure timestamps are Date objects)
  activities.sort((a, b) => {
    const timeA = a.timestamp instanceof Date ? a.timestamp.getTime() : new Date(a.timestamp).getTime();
    const timeB = b.timestamp instanceof Date ? b.timestamp.getTime() : new Date(b.timestamp).getTime();
    return timeB - timeA;
  });

  const getStatusIcon = (status?: string) => {
    switch (status) {
      case 'success':
        return <CheckCircle className="h-4 w-4 text-green-500" />;
      case 'error':
        return <AlertCircle className="h-4 w-4 text-red-500" />;
      case 'pending':
        return <Clock className="h-4 w-4 text-yellow-500 animate-pulse" />;
      default:
        return null;
    }
  };

  const getActivityColor = (type: string) => {
    switch (type) {
      case 'insight':
        return 'text-yellow-600 bg-yellow-50';
      case 'agent':
        return 'text-blue-600 bg-blue-50';
      case 'learning':
        return 'text-purple-600 bg-purple-50';
      case 'source':
        return 'text-green-600 bg-green-50';
      case 'knowledge':
        return 'text-indigo-600 bg-indigo-50';
      default:
        return 'text-gray-600 bg-gray-50';
    }
  };

  // Mock activity items to match the image
  const mockActivities = [
    {
      id: 'insight-1',
      type: 'insight',
      title: 'New Insight Generated',
      description: 'The combination of attention mechanisms and positional enc...',
      timestamp: 'about 2 hours ago',
      icon: Lightbulb,
      iconColor: 'text-yellow-600',
      status: 'success'
    },
    {
      id: 'learning-1',
      type: 'learning',
      title: 'Learning: concept_learned',
      description: 'Knowledge updated',
      timestamp: 'about 2 hours ago',
      icon: Brain,
      iconColor: 'text-blue-600',
      status: 'success'
    },
    {
      id: 'learning-2',
      type: 'learning',
      title: 'Learning: connection_formed',
      description: 'Knowledge updated',
      timestamp: 'about 2 hours ago',
      icon: Link,
      iconColor: 'text-purple-600',
      status: 'success'
    }
  ];

  return (
    <Card className="border-0 shadow-sm h-full">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-blue-50 text-blue-600">
            <Activity className="h-5 w-5" />
          </div>
          Activity Feed
        </CardTitle>
      </CardHeader>
      <CardContent>
        <ScrollArea className="h-[400px] pr-4">
          <div className="space-y-4">
            {mockActivities.map((activity) => {
              const Icon = activity.icon;
              return (
                <div
                  key={activity.id}
                  className="flex gap-3 p-3 rounded-lg hover:bg-accent/50 transition-colors cursor-pointer"
                >
                  <div className="h-8 w-8 rounded-full flex items-center justify-center bg-gray-100">
                    <Icon className={cn("h-4 w-4", activity.iconColor)} />
                  </div>
                  <div className="flex-1 space-y-1">
                    <div className="flex items-center gap-2">
                      <p className="font-medium text-sm">{activity.title}</p>
                      {activity.status === 'success' && (
                        <CheckCircle className="h-4 w-4 text-green-500" />
                      )}
                    </div>
                    <p className="text-sm text-muted-foreground">
                      {activity.description}
                    </p>
                    <p className="text-xs text-muted-foreground">
                      {activity.timestamp}
                    </p>
                  </div>
                </div>
              );
            })}
          </div>
        </ScrollArea>
      </CardContent>
    </Card>
  );
}