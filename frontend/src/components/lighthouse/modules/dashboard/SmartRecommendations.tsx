import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from '~/components/ui/card';
import { <PERSON><PERSON> } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import {
  <PERSON>rkles,
  ArrowRight,
  FileText,
  Search,
  Bot,
  Link2,
  TrendingUp,
  AlertCircle,
} from 'lucide-react';
import { cn } from '~/lib/utils';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';

export function SmartRecommendations() {
  const {
    suggestions,
    currentProject,
    navigateToModule,
    deployAgent,
    addKnowledgeItem,
  } = useLighthouseStore();

  // Get top suggestions or generate default ones
  const topSuggestions = suggestions.slice(0, 3);

  // If no suggestions, create some based on project state
  if (topSuggestions.length === 0 && currentProject) {
    const defaultSuggestions = [];

    // Suggest adding sources if none exist
    if (!currentProject.intelligence.keyInsights.length) {
      defaultSuggestions.push({
        id: 'add-sources',
        type: 'task' as const,
        priority: 'high' as const,
        title: 'Add Your First Sources',
        description: 'Upload documents or add URLs to start building your knowledge base',
        rationale: 'Sources provide the foundation for AI-powered insights',
        confidence: 0.9,
        action: {
          type: 'navigate',
          description: 'Go to Sources',
          parameters: { module: 'sources' },
          estimatedImpact: 0.8,
          estimatedTime: 5,
        },
        evidence: [],
      });
    }

    // Suggest research if sources exist but no recent activity
    if (currentProject.intelligence.learningLevel < 20) {
      defaultSuggestions.push({
        id: 'start-research',
        type: 'research' as const,
        priority: 'medium' as const,
        title: 'Explore Your Knowledge',
        description: 'Start a research session to discover insights from your sources',
        rationale: 'Research helps identify patterns and connections in your data',
        confidence: 0.85,
        action: {
          type: 'navigate',
          description: 'Start Research',
          parameters: { module: 'research' },
          estimatedImpact: 0.7,
          estimatedTime: 15,
        },
        evidence: [],
      });
    }

    // Suggest agent deployment for automation
    if (currentProject.intelligence.learningLevel > 30) {
      defaultSuggestions.push({
        id: 'deploy-agent',
        type: 'optimization' as const,
        priority: 'medium' as const,
        title: 'Automate Research Tasks',
        description: 'Deploy an AI agent to continuously monitor and analyze new information',
        rationale: 'Agents can work autonomously while you focus on strategic decisions',
        confidence: 0.8,
        action: {
          type: 'agent',
          description: 'Configure Agent',
          parameters: { module: 'agents' },
          estimatedImpact: 0.9,
          estimatedTime: 10,
        },
        evidence: [],
      });
    }

    topSuggestions.push(...defaultSuggestions);
  }

  const handleAction = async (suggestion: any) => {
    if (suggestion.action) {
      switch (suggestion.action.type) {
        case 'navigate':
          navigateToModule(suggestion.action.parameters.module);
          break;
        case 'agent':
          // TODO: Open agent configuration
          navigateToModule('agents');
          break;
        default:
          console.log('Unknown action type:', suggestion.action.type);
      }
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high':
        return 'destructive';
      case 'medium':
        return 'secondary';
      case 'low':
        return 'outline';
      default:
        return 'secondary';
    }
  };

  const getTypeIcon = (type: string) => {
    switch (type) {
      case 'research':
        return Search;
      case 'connection':
        return Link2;
      case 'task':
        return Bot;
      case 'insight':
        return TrendingUp;
      case 'correction':
        return AlertCircle;
      case 'optimization':
        return Sparkles;
      default:
        return FileText;
    }
  };

  // Mock recommendations to match the image
  const mockRecommendations = [
    {
      id: 'add-sources',
      title: 'Add Your First Sources',
      description: 'Upload documents or add URLs to start building your knowledge base. Why: Sources provide the foundation for AI-powered insights.',
      priority: 'High',
      impact: '80%',
      time: '5m',
      action: 'Go to Sources'
    },
    {
      id: 'explore-knowledge',
      title: 'Explore Your Knowledge',
      description: 'Start a research session to discover insights from your sources. Why: Research helps identify patterns and connections in your data.',
      priority: 'medium',
      impact: '70%',
      time: '15m',
      action: 'Start Research'
    }
  ];

  return (
    <Card className="border-0 shadow-sm">
      <CardHeader className="pb-4">
        <CardTitle className="flex items-center gap-3">
          <div className="p-2 rounded-lg bg-purple-50 text-purple-600">
            <Sparkles className="h-5 w-5" />
          </div>
          Smart Recommendations
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {mockRecommendations.map((recommendation) => (
            <div
              key={recommendation.id}
              className="p-4 rounded-lg border bg-card space-y-3"
            >
              <div className="flex items-start justify-between gap-2">
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <p className="font-medium text-sm">{recommendation.title}</p>
                    <Badge
                      variant={recommendation.priority === 'High' ? 'destructive' : 'secondary'}
                      className="text-xs"
                    >
                      {recommendation.priority}
                    </Badge>
                  </div>
                  <p className="text-sm text-muted-foreground">
                    {recommendation.description}
                  </p>
                </div>
              </div>

              <div className="flex items-center justify-between">
                <div className="flex items-center gap-4 text-xs text-muted-foreground">
                  <span>Impact: {recommendation.impact}</span>
                  <span>Time: {recommendation.time}</span>
                </div>
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => navigateToModule('sources')}
                  className="text-blue-600 hover:text-blue-700"
                >
                  {recommendation.action}
                  <ArrowRight className="h-3 w-3 ml-1" />
                </Button>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}