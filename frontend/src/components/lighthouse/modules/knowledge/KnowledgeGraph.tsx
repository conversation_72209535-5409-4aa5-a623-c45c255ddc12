import React, { useState, useRef, useEffect } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Badge } from '~/components/ui/badge';
import { Slider } from '~/components/ui/slider';
import {
  Network,
  ZoomIn,
  ZoomOut,
  RotateCcw,
  Settings,
  Download,
  Maximize,
  Filter,
  Eye,
  Layers,
  Shuffle,
  Target,
} from 'lucide-react';
import { cn } from '~/lib/utils';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';

interface KnowledgeGraphProps {
  searchQuery: string;
}

interface GraphNode {
  id: string;
  label: string;
  type: 'concept' | 'entity' | 'fact' | 'question' | 'source';
  size: number;
  color: string;
  connections: number;
  importance: number;
}

interface GraphEdge {
  id: string;
  source: string;
  target: string;
  type: string;
  weight: number;
  label?: string;
}

export function KnowledgeGraph({ searchQuery }: KnowledgeGraphProps) {
  const [zoomLevel, setZoomLevel] = useState(100);
  const [selectedNode, setSelectedNode] = useState<string | null>(null);
  const [layoutType, setLayoutType] = useState<'force' | 'hierarchical' | 'circular'>('force');
  const [filterStrength, setFilterStrength] = useState([0.3]);
  const [showLabels, setShowLabels] = useState(true);
  const [highlightConnections, setHighlightConnections] = useState(true);
  
  const graphRef = useRef<HTMLDivElement>(null);
  const {
    knowledgeGraph,
    currentProject,
    insights,
    updateKnowledgeGraph,
  } = useLighthouseStore();

  // Generate mock graph data based on project knowledge
  const generateGraphData = (): { nodes: GraphNode[], edges: GraphEdge[] } => {
    const nodes: GraphNode[] = [];
    const edges: GraphEdge[] = [];

    if (!currentProject) return { nodes, edges };

    // Add concept nodes
    currentProject.intelligence.domainExpertise.concepts.forEach((concept, index) => {
      nodes.push({
        id: concept.id,
        label: concept.name,
        type: 'concept',
        size: 20 + (concept.confidence * 30),
        color: '#3b82f6', // blue
        connections: concept.relationships.length,
        importance: concept.confidence,
      });

      // Add relationship edges
      concept.relationships.forEach((rel) => {
        edges.push({
          id: `${concept.id}-${rel.targetId}`,
          source: concept.id,
          target: rel.targetId,
          type: rel.type,
          weight: rel.strength,
          label: rel.type,
        });
      });
    });

    // Add insight nodes
    insights.slice(0, 10).forEach((insight) => {
      nodes.push({
        id: insight.id,
        label: insight.content.substring(0, 30) + '...',
        type: 'fact',
        size: 15 + (insight.confidence * 20),
        color: '#f59e0b', // yellow
        connections: insight.connections.length,
        importance: insight.confidence,
      });

      // Connect insights to related concepts
      insight.connections.forEach((connectionId) => {
        if (nodes.find(n => n.id === connectionId)) {
          edges.push({
            id: `${insight.id}-${connectionId}`,
            source: insight.id,
            target: connectionId,
            type: 'relates-to',
            weight: 0.7,
          });
        }
      });
    });

    // Add vocabulary as entity nodes
    currentProject.intelligence.domainExpertise.vocabulary
      .filter(v => v.frequency > 2)
      .slice(0, 15)
      .forEach((term) => {
        const nodeId = `vocab-${term.term}`;
        nodes.push({
          id: nodeId,
          label: term.term,
          type: 'entity',
          size: 10 + (term.frequency * 2),
          color: '#10b981', // green
          connections: 0,
          importance: term.frequency / 10,
        });
      });

    return { nodes, edges };
  };

  const { nodes, edges } = generateGraphData();

  // Filter nodes based on search and strength
  const filteredNodes = nodes.filter(node => {
    const matchesSearch = !searchQuery || 
      node.label.toLowerCase().includes(searchQuery.toLowerCase());
    const meetsStrength = node.importance >= filterStrength[0];
    return matchesSearch && meetsStrength;
  });

  const filteredEdges = edges.filter(edge => {
    const sourceExists = filteredNodes.find(n => n.id === edge.source);
    const targetExists = filteredNodes.find(n => n.id === edge.target);
    return sourceExists && targetExists && edge.weight >= filterStrength[0];
  });

  const handleNodeClick = (nodeId: string) => {
    setSelectedNode(selectedNode === nodeId ? null : nodeId);
  };

  const getNodeTypeColor = (type: string) => {
    switch (type) {
      case 'concept': return 'bg-blue-100 text-blue-800';
      case 'entity': return 'bg-green-100 text-green-800';
      case 'fact': return 'bg-yellow-100 text-yellow-800';
      case 'question': return 'bg-purple-100 text-purple-800';
      case 'source': return 'bg-gray-100 text-gray-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  const selectedNodeData = selectedNode ? nodes.find(n => n.id === selectedNode) : null;

  return (
    <div className="flex h-full gap-6">
      {/* Graph Canvas */}
      <div className="flex-1 space-y-4">
        {/* Graph Controls */}
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <div className="flex items-center gap-2">
                  <Button size="sm" variant="outline" onClick={() => setZoomLevel(z => Math.max(25, z - 25))}>
                    <ZoomOut className="h-4 w-4" />
                  </Button>
                  <span className="text-sm font-medium w-12">{zoomLevel}%</span>
                  <Button size="sm" variant="outline" onClick={() => setZoomLevel(z => Math.min(300, z + 25))}>
                    <ZoomIn className="h-4 w-4" />
                  </Button>
                </div>

                <div className="flex items-center gap-2">
                  <span className="text-sm text-muted-foreground">Layout:</span>
                  <Button
                    size="sm"
                    variant={layoutType === 'force' ? 'default' : 'outline'}
                    onClick={() => setLayoutType('force')}
                    className={layoutType === 'force' ? 'bg-blue-500 hover:bg-blue-600' : ''}
                  >
                    Force
                  </Button>
                  <Button
                    size="sm"
                    variant={layoutType === 'hierarchical' ? 'default' : 'outline'}
                    onClick={() => setLayoutType('hierarchical')}
                    className={layoutType === 'hierarchical' ? 'bg-blue-500 hover:bg-blue-600' : ''}
                  >
                    Tree
                  </Button>
                  <Button
                    size="sm"
                    variant={layoutType === 'circular' ? 'default' : 'outline'}
                    onClick={() => setLayoutType('circular')}
                    className={layoutType === 'circular' ? 'bg-blue-500 hover:bg-blue-600' : ''}
                  >
                    Circle
                  </Button>
                  <Button
                    size="sm"
                    variant="outline"
                    className="text-blue-500 border-blue-200 hover:bg-blue-50"
                  >
                    Join
                  </Button>
                </div>
              </div>

              <div className="flex items-center gap-2">
                <Button size="sm" variant="outline">
                  <Download className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="outline">
                  <Maximize className="h-4 w-4" />
                </Button>
                <Button size="sm" variant="outline">
                  <Settings className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Graph Visualization */}
        <Card className="flex-1">
          <CardContent className="p-0 h-full">
            <div
              ref={graphRef}
              className="w-full h-full min-h-[500px] bg-muted/20 relative overflow-hidden rounded-lg"
              style={{ transform: `scale(${zoomLevel / 100})` }}
            >
              {/* Mock Graph Visualization */}
              <svg className="w-full h-full">
                {/* Grid Background */}
                <defs>
                  <pattern id="grid" width="20" height="20" patternUnits="userSpaceOnUse">
                    <path d="M 20 0 L 0 0 0 20" fill="none" stroke="#e5e7eb" strokeWidth="0.5" opacity="0.5"/>
                  </pattern>
                </defs>
                <rect width="100%" height="100%" fill="url(#grid)" />

                {/* Edges */}
                {filteredEdges.map((edge, index) => {
                  const sourceNode = filteredNodes.find(n => n.id === edge.source);
                  const targetNode = filteredNodes.find(n => n.id === edge.target);
                  if (!sourceNode || !targetNode) return null;

                  const sourceX = (index % 8) * 120 + 100;
                  const sourceY = Math.floor(index / 8) * 100 + 100;
                  const targetX = ((index + 1) % 8) * 120 + 100;
                  const targetY = Math.floor((index + 1) / 8) * 100 + 100;

                  return (
                    <line
                      key={edge.id}
                      x1={sourceX}
                      y1={sourceY}
                      x2={targetX}
                      y2={targetY}
                      stroke="#94a3b8"
                      strokeWidth={edge.weight * 3}
                      opacity={0.6}
                    />
                  );
                })}

                {/* Nodes */}
                {filteredNodes.map((node, index) => {
                  const x = (index % 8) * 120 + 100;
                  const y = Math.floor(index / 8) * 100 + 100;
                  const isSelected = selectedNode === node.id;

                  return (
                    <g key={node.id}>
                      <circle
                        cx={x}
                        cy={y}
                        r={node.size}
                        fill={node.color}
                        stroke={isSelected ? '#000' : '#fff'}
                        strokeWidth={isSelected ? 3 : 2}
                        className="cursor-pointer hover:opacity-80"
                        onClick={() => handleNodeClick(node.id)}
                      />
                      {showLabels && (
                        <text
                          x={x}
                          y={y + node.size + 15}
                          textAnchor="middle"
                          fontSize="12"
                          fill="#374151"
                          className="pointer-events-none"
                        >
                          {node.label.length > 15 ? node.label.substring(0, 15) + '...' : node.label}
                        </text>
                      )}
                    </g>
                  );
                })}
              </svg>

              {/* Graph Stats Overlay */}
              <div className="absolute top-4 left-4 bg-white/90 backdrop-blur-sm rounded-lg p-3 space-y-1">
                <div className="text-sm font-medium">Graph Stats</div>
                <div className="text-xs text-muted-foreground space-y-1">
                  <div>Nodes: {filteredNodes.length}</div>
                  <div>Edges: {filteredEdges.length}</div>
                  <div>Density: {((filteredEdges.length / Math.max(1, filteredNodes.length - 1)) * 100).toFixed(1)}%</div>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Right Sidebar - Controls & Details */}
      <aside className="w-80 space-y-6">
        {/* Graph Filters */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Filter className="h-4 w-4" />
              Graph Filters
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div>
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium">Connection Strength</span>
                <span className="text-sm text-muted-foreground">
                  {Math.round(filterStrength[0] * 100)}%
                </span>
              </div>
              <Slider
                value={filterStrength}
                onValueChange={setFilterStrength}
                max={1}
                step={0.1}
                className="w-full"
              />
            </div>

            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Show Labels</span>
                <Button
                  size="sm"
                  variant={showLabels ? 'default' : 'outline'}
                  onClick={() => setShowLabels(!showLabels)}
                >
                  <Eye className="h-4 w-4" />
                </Button>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm font-medium">Highlight Connections</span>
                <Button
                  size="sm"
                  variant={highlightConnections ? 'default' : 'outline'}
                  onClick={() => setHighlightConnections(!highlightConnections)}
                >
                  <Network className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Node Types Legend */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm flex items-center gap-2">
              <Layers className="h-4 w-4" />
              Node Types
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              {[
                { type: 'concept', label: 'Concepts', count: nodes.filter(n => n.type === 'concept').length },
                { type: 'entity', label: 'Entities', count: nodes.filter(n => n.type === 'entity').length },
                { type: 'fact', label: 'Facts/Insights', count: nodes.filter(n => n.type === 'fact').length },
                { type: 'question', label: 'Questions', count: nodes.filter(n => n.type === 'question').length },
                { type: 'source', label: 'Sources', count: nodes.filter(n => n.type === 'source').length },
              ].map(({ type, label, count }) => (
                <div key={type} className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <Badge variant="secondary" className={getNodeTypeColor(type)}>
                      {label}
                    </Badge>
                  </div>
                  <span className="text-sm text-muted-foreground">{count}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Selected Node Details */}
        {selectedNodeData && (
          <Card>
            <CardHeader className="pb-3">
              <CardTitle className="text-sm flex items-center gap-2">
                <Target className="h-4 w-4" />
                Node Details
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-3">
                <div>
                  <h4 className="font-medium">{selectedNodeData.label}</h4>
                  <Badge variant="secondary" className={getNodeTypeColor(selectedNodeData.type)}>
                    {selectedNodeData.type}
                  </Badge>
                </div>
                
                <div className="space-y-2 text-sm">
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Connections:</span>
                    <span>{selectedNodeData.connections}</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Importance:</span>
                    <span>{Math.round(selectedNodeData.importance * 100)}%</span>
                  </div>
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Node Size:</span>
                    <span>{selectedNodeData.size}</span>
                  </div>
                </div>

                <Button size="sm" className="w-full">
                  Explore Connections
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Quick Actions */}
        <Card>
          <CardHeader className="pb-3">
            <CardTitle className="text-sm">Quick Actions</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2">
              <Button size="sm" variant="outline" className="w-full">
                <Shuffle className="h-4 w-4 mr-2" />
                Randomize Layout
              </Button>
              <Button size="sm" variant="outline" className="w-full">
                <RotateCcw className="h-4 w-4 mr-2" />
                Reset View
              </Button>
              <Button size="sm" variant="outline" className="w-full">
                <Target className="h-4 w-4 mr-2" />
                Find Clusters
              </Button>
            </div>
          </CardContent>
        </Card>
      </aside>
    </div>
  );
}