import React, { useState } from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, CardTitle } from '~/components/ui/card';
import { Button } from '~/components/ui/button';
import { Input } from '~/components/ui/input';
import { Textarea } from '~/components/ui/textarea';
import { Badge } from '~/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '~/components/ui/tabs';
import {
  Plus,
  Link,
  FileText,
  Brain,
  Send,
  Loader2,
  Check,
  Sparkles,
  Tag,
  Zap,
  HelpCircle,
} from 'lucide-react';
import { cn } from '~/lib/utils';
import { useLighthouseStore } from '../../shared/store/lighthouse-store';

type CaptureType = 'note' | 'insight' | 'quest';

export function QuickCapture() {
  const [captureType, setCaptureType] = useState<CaptureType>('note');
  const [content, setContent] = useState('');
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');
  const [isProcessing, setIsProcessing] = useState(false);
  const [showSuccess, setShowSuccess] = useState(false);

  const {
    currentProject,
    addKnowledgeItem,
    addInsight,
    recordLearning,
  } = useLighthouseStore();

  const captureTypes = [
    { id: 'note', icon: FileText, label: 'Note', placeholder: 'Quick note, observation, or question...' },
    { id: 'insight', icon: Zap, label: 'Insight', placeholder: 'Key insight or connection...' },
    { id: 'quest', icon: HelpCircle, label: 'Quest', placeholder: 'Research question or goal...' },
  ];

  const handleCapture = async () => {
    if (!currentProject || !content) return;

    setIsProcessing(true);

    try {
      const timestamp = new Date();

      if (captureType === 'insight') {
        // Process insight
        addInsight({
          content,
          confidence: 0.7,
          sources: [],
          connections: [],
          impact: 'medium',
        });

        recordLearning({
          type: 'concept',
          trigger: 'Manual insight capture',
          before: {
            confidence: 0.5,
            understanding: 'Prior knowledge state',
            relatedNodes: [],
            supportingEvidence: [],
          },
          after: {
            confidence: 0.7,
            understanding: content,
            relatedNodes: [],
            supportingEvidence: ['user-input'],
          },
        });
      } else {
        // Process note or quest
        await addKnowledgeItem({
          projectId: currentProject.id,
          type: 'note',
          content: {
            text: content,
            type: captureType,
          },
          metadata: {
            confidence: 0.6,
            tags,
            created: timestamp,
            lastAccessed: timestamp,
            accessCount: 1,
            quality: 'pending',
          },
        });
      }

      // Show success and reset form
      setShowSuccess(true);
      setContent('');
      setTags([]);

      setTimeout(() => setShowSuccess(false), 2000);
    } catch (error) {
      console.error('Capture failed:', error);
    } finally {
      setIsProcessing(false);
    }
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tag: string) => {
    setTags(tags.filter(t => t !== tag));
  };

  const currentType = captureTypes.find(t => t.id === captureType);

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-sm flex items-center gap-2">
          <Zap className="h-4 w-4 text-pink-500" />
          Quick Capture
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Capture Type Tabs */}
        <Tabs value={captureType} onValueChange={(value) => setCaptureType(value as CaptureType)}>
          <TabsList className="grid w-full grid-cols-3">
            {captureTypes.map(({ id, icon: Icon, label }) => (
              <TabsTrigger key={id} value={id} className="flex items-center gap-1 text-xs">
                <Icon className="h-3 w-3" />
                {label}
              </TabsTrigger>
            ))}
          </TabsList>

          {/* Input Field */}
          <div className="mt-4">
            <Textarea
              placeholder={currentType?.placeholder}
              value={content}
              onChange={(e) => setContent(e.target.value)}
              className="text-sm min-h-[80px] resize-none"
            />
          </div>
        </Tabs>

        {/* Tags */}
        <div>
          <div className="flex items-center gap-2 mb-2">
            <Input
              placeholder="Add tag..."
              value={newTag}
              onChange={(e) => setNewTag(e.target.value)}
              onKeyDown={(e) => e.key === 'Enter' && addTag()}
              className="text-sm flex-1"
            />
            <Button size="sm" variant="outline" onClick={addTag}>
              <Tag className="h-3 w-3" />
            </Button>
          </div>
          
          {tags.length > 0 && (
            <div className="flex flex-wrap gap-1">
              {tags.map((tag) => (
                <Badge
                  key={tag}
                  variant="secondary"
                  className="text-xs cursor-pointer hover:bg-destructive hover:text-destructive-foreground"
                  onClick={() => removeTag(tag)}
                >
                  {tag} ×
                </Badge>
              ))}
            </div>
          )}
        </div>

        {/* Capture Button */}
        <Button
          onClick={handleCapture}
          disabled={isProcessing || !content || !currentProject}
          className="w-full bg-pink-500 hover:bg-pink-600"
          size="sm"
        >
          {isProcessing ? (
            <>
              <Loader2 className="h-4 w-4 mr-2 animate-spin" />
              Processing...
            </>
          ) : showSuccess ? (
            <>
              <Check className="h-4 w-4 mr-2" />
              Captured!
            </>
          ) : (
            <>
              <Send className="h-4 w-4 mr-2" />
              Capture Knowledge
            </>
          )}
        </Button>

        {/* Recent Captures */}
        <div className="pt-2 border-t">
          <p className="text-xs text-muted-foreground mb-2">Recent captures will appear here</p>
          <div className="space-y-1">
            <div className="text-xs text-muted-foreground italic">
              No recent captures yet.<br />
              Use the form above to add one.
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}