import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { cn, animations, typography, layout, visualEffects } from '~/lib/ui-utils';
import { ModuleName, ModuleRoute } from '../types/navigation.types';
import { useLighthouseStore } from '../shared/store/lighthouse-store';
import { AnimatedBorderWrapper } from '~/components/ui/AnimatedBorderWrapper';
import { HoverLift, FocusRing } from '../shared/EnhancedMicrointeractions';
import { SparklesIcon, ClockIcon } from '../shared/icons';

interface ModuleContainerProps {
  module: ModuleName;
  moduleInfo: ModuleRoute;
  children: React.ReactNode;
  className?: string;
}

export function ModuleContainer({
  module,
  moduleInfo,
  children,
  className,
}: ModuleContainerProps) {
  const [isTransitioning, setIsTransitioning] = useState(false);
  const [previousModule, setPreviousModule] = useState<ModuleName | null>(null);
  const { currentProject, updateIntelligenceContext } = useLighthouseStore();

  useEffect(() => {
    // Track module changes for transition direction
    if (previousModule && previousModule !== module) {
      setIsTransitioning(true);
      const timer = setTimeout(() => setIsTransitioning(false), 500);

      // Update intelligence context with current module focus
      if (currentProject) {
        updateIntelligenceContext({
          currentFocus: `Using ${moduleInfo.label} module`,
        });
      }

      return () => clearTimeout(timer);
    }

    setPreviousModule(module);
  }, [module, moduleInfo, currentProject, updateIntelligenceContext, previousModule]);

  const slideVariants = {
    enter: (direction: number) => ({
      x: direction > 0 ? 300 : -300,
      opacity: 0,
      scale: 0.95,
    }),
    center: {
      x: 0,
      opacity: 1,
      scale: 1,
    },
    exit: (direction: number) => ({
      x: direction < 0 ? 300 : -300,
      opacity: 0,
      scale: 0.95,
    }),
  };

  const headerVariants = {
    hidden: { opacity: 0, y: -20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.4, ease: "easeOut" }
    },
  };

  return (
    <motion.div
      className={cn("flex flex-col h-full overflow-hidden", className)}
      initial="hidden"
      animate="visible"
      variants={{
        hidden: { opacity: 0 },
        visible: { opacity: 1, transition: { duration: 0.3 } }
      }}
    >
      {/* Module Header - matches the image layout */}
      <motion.header
        className={cn(
          "border-b px-6 py-4 backdrop-blur-sm bg-background",
          visualEffects.shadows.soft
        )}
        variants={headerVariants}
        initial="hidden"
        animate="visible"
      >
        <div className="flex items-center justify-center">
          <div className="flex items-center gap-4">
            <HoverLift liftHeight={2} scale={1.05} duration={0.2}>
              <FocusRing ringColor="rgb(59, 130, 246)">
                <motion.div
                  initial={{ scale: 0, rotate: -180 }}
                  animate={{ scale: 1, rotate: 0 }}
                  transition={{ duration: 0.5, type: "spring", stiffness: 200 }}
                  className={cn(
                    "p-2 rounded-lg transition-all duration-300",
                    "bg-pink-50 text-pink-600",
                    "cursor-pointer"
                  )}
                  whileHover={{ rotate: 5 }}
                  whileTap={{ scale: 0.95 }}
                >
                  <SparklesIcon size={20} />
                </motion.div>
              </FocusRing>
            </HoverLift>
            <div>
              <motion.h2
                className={cn(typography.h2, "text-foreground")}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.1 }}
              >
                {moduleInfo.label}
              </motion.h2>
              <motion.p
                className={cn(typography.bodySmall, "text-muted-foreground mt-1")}
                initial={{ opacity: 0, x: -20 }}
                animate={{ opacity: 1, x: 0 }}
                transition={{ duration: 0.4, delay: 0.2 }}
              >
                {moduleInfo.description}
              </motion.p>
            </div>
          </div>
        </div>
      </motion.header>

      {/* Module Content */}
      <div className="flex-1 overflow-hidden relative">
        <AnimatePresence mode="wait" custom={1}>
          <motion.div
            key={module}
            custom={1}
            variants={slideVariants}
            initial="enter"
            animate="center"
            exit="exit"
            transition={{
              x: { type: "spring", stiffness: 300, damping: 30 },
              opacity: { duration: 0.2 },
              scale: { duration: 0.2 }
            }}
            className="h-full overflow-auto"
          >
            {currentProject ? (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ duration: 0.4, delay: 0.2 }}
                className="h-full"
              >
                {children}
              </motion.div>
            ) : (
              <motion.div
                className="flex items-center justify-center h-full"
                initial={{ opacity: 0, scale: 0.9 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ duration: 0.4 }}
              >
                <HoverLift liftHeight={4} scale={1.02} duration={0.3}>
                  <AnimatedBorderWrapper
                    isLoading={false}
                    variant="pulse"
                    borderColor="#6b7280"
                    className="rounded-xl"
                  >
                    <div className={cn(
                      "text-center space-y-6 max-w-md p-8 rounded-xl",
                      visualEffects.cards.glass
                    )}>
                      <motion.div
                        initial={{ scale: 0 }}
                        animate={{ scale: 1 }}
                        transition={{ duration: 0.5, type: "spring" }}
                        className={cn(
                          "mx-auto w-16 h-16 rounded-full flex items-center justify-center",
                          visualEffects.gradients.subtle,
                          visualEffects.shadows.medium
                        )}
                      >
                        <ClockIcon size={24} className="text-muted-foreground" />
                      </motion.div>
                    <div>
                      <motion.h3
                        className={cn(typography.h3, "mb-2")}
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.4, delay: 0.1 }}
                      >
                        No Project Selected
                      </motion.h3>
                      <motion.p
                        className="text-muted-foreground"
                        initial={{ opacity: 0, y: 10 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.4, delay: 0.2 }}
                      >
                        Select or create a project to start using the {moduleInfo.label} module.
                      </motion.p>
                    </div>
                    </div>
                  </AnimatedBorderWrapper>
                </HoverLift>
              </motion.div>
            )}
          </motion.div>
        </AnimatePresence>
      </div>
    </motion.div>
  );
}