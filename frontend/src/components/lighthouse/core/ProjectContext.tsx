import React from 'react';
import { <PERSON><PERSON><PERSON>D<PERSON>, Brain, T<PERSON>dingUp, Lightbulb, Plus } from 'lucide-react';
import { cn } from '~/lib/utils';
import { Button } from '~/components/ui/button';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '~/components/ui/dropdown-menu';
import { Badge } from '~/components/ui/badge';
import { Progress } from '~/components/ui/progress';
import { useLighthouseStore } from '../shared/store/lighthouse-store';

export function ProjectContext() {
  const {
    currentProject,
    projects,
    projectContext,
    insights,
    activeAgents,
    setCurrentProject,
    createProject,
  } = useLighthouseStore();

  const handleCreateProject = async () => {
    // TODO: Open project creation modal
    const newProject = await createProject({
      name: 'New Project',
      description: 'A new Lighthouse project',
      goal: 'Define your project goal',
      domain: 'general',
      status: 'planning',
      intelligence: {
        contextId: '',
        knowledgeGraphId: '',
        learningLevel: 0,
        keyInsights: [],
        activePatterns: [],
        domainExpertise: {
          primaryDomain: 'general',
          relatedDomains: [],
          concepts: [],
          vocabulary: [],
          expertiseLevel: 0,
        },
      },
      metadata: {
        tags: [],
        collaborators: [],
        visibility: 'private',
        settings: {
          autoLearn: true,
          shareInsights: false,
          agentAutonomy: 'medium',
          sourcePriority: 'relevance',
        },
      },
    });
    setCurrentProject(newProject.id);
  };

  if (!currentProject) {
    return (
      <div className="h-16 border-b flex items-center justify-between px-6 bg-muted/50">
        <div className="flex items-center gap-4">
          <h1 className="text-xl font-semibold">Lighthouse</h1>
          <span className="text-muted-foreground">Select or create a project to begin</span>
        </div>
        <Button onClick={handleCreateProject} size="sm">
          <Plus className="h-4 w-4 mr-2" />
          New Project
        </Button>
      </div>
    );
  }

  const recentInsights = insights
    .filter(i => i.timestamp > new Date(Date.now() - 24 * 60 * 60 * 1000))
    .length;
  
  const learningLevel = currentProject.intelligence.learningLevel;
  const runningAgentsCount = activeAgents.filter(a => a.projectId === currentProject.id).length;

  return (
    <div className="h-16 border-b flex items-center justify-between px-6 bg-background">
      {/* Left - Project Info */}
      <div className="flex items-center gap-4">
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="ghost" className="gap-2">
              <div className="text-left">
                <div className="font-semibold">New Project</div>
                <div className="text-xs text-muted-foreground">
                  Planning • General
                </div>
              </div>
              <ChevronDown className="h-4 w-4 opacity-50" />
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="start" className="w-[250px]">
            {projects.map((project) => (
              <DropdownMenuItem
                key={project.id}
                onClick={() => setCurrentProject(project.id)}
                className={cn(
                  "flex flex-col items-start gap-1 py-3",
                  project.id === currentProject.id && "bg-accent"
                )}
              >
                <span className="font-medium">{project.name}</span>
                <span className="text-xs text-muted-foreground">
                  {project.domain} • Learning: {project.intelligence.learningLevel}%
                </span>
              </DropdownMenuItem>
            ))}
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={handleCreateProject}>
              <Plus className="h-4 w-4 mr-2" />
              New Project
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>
      </div>

      {/* Right - Status Indicators */}
      <div className="flex items-center gap-6">
        {/* Learning */}
        <div className="flex items-center gap-2">
          <div className="flex flex-col items-center">
            <span className="text-xs text-muted-foreground">Learning</span>
            <span className="text-sm font-medium">0%</span>
          </div>
        </div>

        {/* Insights */}
        <div className="flex items-center gap-2">
          <div className="flex flex-col items-center">
            <span className="text-xs text-muted-foreground">Insights (24h)</span>
            <span className="text-sm font-medium">0</span>
          </div>
        </div>

        {/* Expertise */}
        <div className="flex items-center gap-2">
          <div className="flex flex-col items-center">
            <span className="text-xs text-muted-foreground">Expertise</span>
            <div className="flex items-center gap-1">
              {[...Array(5)].map((_, i) => (
                <div
                  key={i}
                  className="h-1.5 w-3 rounded-full bg-muted"
                />
              ))}
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}